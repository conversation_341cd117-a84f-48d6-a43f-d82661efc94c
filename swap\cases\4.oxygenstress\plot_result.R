# Wageningen Environmental Research
# <EMAIL>
#------------------------------------------------

# set start of program
TIMPRGSTART <- Sys.time()

# set modus
test <- FALSE

# read arguments
#------------------------------------------------

if (!test) {
   command_args <- commandArgs(trailingOnly = TRUE)
   if (length(command_args) != 0) {
     warning("ERROR: wrong usage of script")
     warning("Arguments: none")
     stop()
    }
  } else {
   command_args <- NULL
   setwd("d:/cases/4.oxygenstress")
  }
ctrl <- command_args[1]

# load libraries
#------------------------------------------------
source("../../Rsoftware/libraries.R")

# run R-script
#------------------------------------------------
message(str_c("\nProgram R-PROG started...\n"))

# ---- initial part of procedure ----

if(!interactive()) pdf(NULL)

file_swp <- "./swap.swp"

# ---- main part of procedure ----

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output.csv")
db_swp <- read_csv_SWAP(file = file_csv, variable = c("PGRASSDM", "GRASSDM", "PMOWDM", "MOWDM", "TREDDRY", "TREDWET")) %>%
  mutate(
    date = as_date(datetime),
    potential = PGRASSDM + PMOWDM,
    actual = GRASSDM + MOWDM,
    drought = TREDDRY,
    oxygen = TREDWET
  ) %>%
  select(date, potential, actual, drought, oxygen)

# ---- crop development ----

# rearrange data
db_fig <- NULL
levels <- c("potential", "actual")
for (level in levels) {
  db_tmp <- db_swp %>%
    rename(value = all_of(level)) %>%
    mutate(level = all_of(level)) %>%
    select(date, level, value)
  db_fig <- rbind(db_fig, db_tmp)
}

# create factors
db_fig$level <- factor(x = db_fig$level, levels = levels, ordered = TRUE)

# create plot
G1 <- ggplot(data = db_fig) +
  geom_line(aes(x = date, y = value, colour = level)) +
  scale_colour_manual(name = create_label(label = "growth"), values = c("potential" = "green", "actual" = "black")) +
  labs(x = "", y = create_label(label = "Yield", unit = "kg ha-1")) +
  get_my_theme("figure")

# ---- stress ----

# rearrange data
db_fig <- NULL
levels <- c("drought", "oxygen")
for (level in levels) {
  db_tmp <- db_swp %>%
    rename(value = all_of(level)) %>%
    mutate(level = all_of(level)) %>%
    select(date, level, value)
  db_fig <- rbind(db_fig, db_tmp)
}

# create factors
db_fig$level <- factor(x = db_fig$level, levels = levels, ordered = TRUE)

# create plot
G2 <- ggplot(data = db_fig) +
  geom_col(aes(x = date, y = value * 10, fill = level), position = "stack") +
  scale_fill_manual(name = create_label(label = "stress"), values = c("drought" = "red", "oxygen" = "blue")) +
  labs(x = "", y = create_label(label = "Transpiration reduction", unit = "mm d-1")) +
  get_my_theme("figure")

# ---- combine plots ----

# combine
P1 <- ggplotGrob(G1)
P2 <- ggplotGrob(G2)
P <- rbind(P1, P2, size = "first")
P$widths <- unit.pmax(P1$widths, P2$widths)

ggsave(filename = str_c(OUTFIL, ".png"), plot = P, width = 10, height = 8, dpi = "retina", bg = "white")


# ---- return part of procedure ----

TIMPRGEND <- Sys.time()
TIMPRGCALC <- as.numeric(difftime(time1 = TIMPRGEND, time2 = TIMPRGSTART, units = "secs"))
message(paste0("\nProgram R-PROG successfully ended in ", floor(TIMPRGCALC / 60), " minutes and ", ceiling(TIMPRGCALC %% 60), " seconds"))
q(save = "no")
