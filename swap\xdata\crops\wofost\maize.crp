***********************************************************************************************
* Filename: maize.crp
* Contents: SWAP 4 - Data for detailed crop model
***********************************************************************************************
* Grain maize (Zea mays L.)
* $Id: mag201.cab 1.3 1997/09/25 14:06:58 LEM release $
* File MAG201.CAB
* CROP DATA FILE for use with WOFOST Version 5.4, June 1992
*
* GRAIN MAIZE 201 
* Regions : Germany, R13, R15, R16, R17 and Luxembourg 
* sowing date 1 May
* mean date of flowering 26 July, mature 20 October

* Derived from SUCROS87 data set for maize.
* Calibrated for use in WOFOST model at the Centre for Agrobiological 
* Research (CABO-DLO) for the simulation of crop growth and yield on the 
* basis of daily weather data.
* Purpose of application: Crop growth monitoring with agrometeorological 
* model in the EC.
* Developed in the framework of JRC Agriculture Project Action 3. 
*
* Input Differences with MAG201.CAB:
* - Input part for additional parameters for ET-calculations (Part 1)
* - Germination also due to soil moisture conditions (HDRYGERM .. AGERM) 
* - Input of Extinction coefficient for direct visible light (KDIR)
* - No input of water use params (CFET,DEPNR,IAIRDU); these are determined by Swap-modules
* - Input part for Soil water extraction by plant roots (Part 11)
* - Input part for salt stress (Part 12)
* - Input part for interception (Part 13)
* - Input of rooting depth (density) as function of depth (RDCTB) (Part 14)
* - Expert-option for rooting depth limitation by relative dry matter increase (Part 14:SWDMI2RD)
* - Input part for stress due to management other than irrigation, e.g. pests,diseases,nutrients,etc..(Part 1)
* - Seperate section for irrigation scheduling (Part 1,2,3)
***********************************************************************************************

*** PLANT GROWTH SECTION ***

***********************************************************************************************
* Part 0 : Preparation, Sowing, Germination and Harvest

* Part 0a: Preparation before crop growth

* Switch for preparation (default SWPREP = 0):
  SWPREP       = 0        ! 0 = no preparation
                          ! 1 = Preparation before start of crop growth

* Part 0b: Sowing
* Switch for preparation (default SWSOW = 0):
  SWSOW        = 0        ! 0 = no sowing
                          ! 1 = Sowing before start of crop growth

* Part 0c: Germination
  
* Switch for germination (default SWGERM = 1):
  SWGERM       = 2        ! 0 = no germination
                          ! 1 = simulate germination depending on temperature
                          ! 2 = simulate germination depending on temperature and hydrological conditions

* If SWGERM = 2, specify:
  TSUMEMEOPT   =  110.0   ! temperature sum needed for crop emergence     [0..1000 C d, R]
  TBASEM       =    4.0   ! minimum temperature, used for germination trajectory  [0..40 C, R]  
  TEFFMX       =   30.0   ! maximum temperature, used for germination trajectory  [0..40 C, R]  
  HDRYGERM     = -500.0   ! pressure head rootzone for dry germination trajectory [-1000..-0.01 cm, R]
  HWETGERM     =  -50.0   ! pressure head rootzone for wet germination trajectory [-100..-0.01 cm, R]
  ZGERM        =  -10.0   ! z-level for monitoring average pressure head [-100..0 cm, R]
  AGERM        =  203.0   ! a-coefficient Eq. 24/25 Feddes & Van Wijk     [1..1000, R]

* Part 0d: Harvest

  DVSEND       = 3.0      ! development stage at harvest [0..3 -, R]
  
* Switch to check work-ability during harvest:
  SWHARV       = 0        ! 0 = timing of harvest depends on end of growing period (CROPEND)
                          ! 1 = timing of harvest depends on development stage (DVSEND)

***********************************************************************************************

***********************************************************************************************
* Part 1: Crop factor or crop height

* Choose between crop factor and crop height
* Choose crop factor if ETref is used, either from meteo input file (SWETR = 1) or with Penman-Monteith
* Choose crop height if Penman-Monteith should be used with actual crop height, albedo and canopy resistance
  SWCF = 2 ! 1 = crop factor 
           ! 2 = crop height

* If SWCF = 1, list crop factor CF [0..2 -, R],   as function of development stage DVS [0..2 -, R]:
* If SWCF = 2, list crop height CH [0..1.d4 cm, R], as function of development stage DVS [0..2 -, R]:

         DVS      CH   CF
         0.0     1.0  0.5
         0.3    15.0  0.8
         0.5    40.0  1.0
         0.7   140.0  1.0
         1.0   170.0  1.0
         1.4   180.0  1.0
         2.0   175.0  1.0
* End of table

* If SWCF = 2, in addition to crop height list crop specific values for:
  ALBEDO =   0.20 ! crop reflection coefficient [0..1.0 -, R]                    
  RSC    =  167.0 ! Minimum canopy resistance [0..1d6 s/m, R]
  RSW    =    0.0 ! Canopy resistance of intercepted water [0..1d6 s/m, R]

***********************************************************************************************

***********************************************************************************************
* Part 2 : Crop development

* Switch for crop development:
  IDSL   = 0 ! 0 = Crop development before anthesis depends on temperature only
             ! 1 = Crop development before anthesis depends on daylength only
             ! 2 = Crop development before anthesis depends on temperature and daylength

* Part 2a :  temperature

* If IDSL = 0 or 2 specify:
  TSUMEA =   695.00 ! Temperature sum from emergence to anthesis [0..10000 C, R]
  TSUMAM =   800.00 ! Temperature sum from anthesis to maturity  [0..10000 C, R]

* Part 2b :  daylength
  
* If IDSL = 1 or 2, specify:
  DLO    =  1.0     ! Optimum daylength for crop development [0..24 h, R]
  DLC    =  0.0     ! Minimum daylength, [0..24 h, R]

** Part 2c : Vernalization
*
** If IDSL = 2 specify:
*  VERNSAT  =       ! saturated vernalisation requirement [0.0,100.0 days, R]
*  VERNBASE =       ! base vernalisation requirement [0.0,100.0 days, R]
*  VERNDVS  =       ! critical development stage after which the effect of vernalisation is halted [0.0,0.3 -, R]
*
**  table with rate of vernalisation as function of tav [days/degrees]
*  VERNRTB =
** End of table

* Part 2d : Development on temperature

* List increase in temperature sum [0..60 C, R] as function of daily average temperature [0..100 C, R]
*         TAV  DTSM    (maximum 15 records)
  DTSMTB =
            0.00   0.00
            6.00   0.00
           30.00  24.00
           35.00  24.00
* End of table

***********************************************************************************************

***********************************************************************************************
* Part 3: Initial values

  TDWI   =   50.00   ! Initial total crop dry weight [0..10000 kg/ha, R]
  LAIEM  = 0.04836 ! Leaf area index at emergence [0..10 m2/m2, R]
  RGRLAI = 0.02940 ! Maximum relative increase in LAI [0..1 m2/m2/d, R]

***********************************************************************************************

***********************************************************************************************
* Part 4: Green surface area

  SPA    =  0.0000 ! Specific pod area  [0..1 ha/kg, R]
  SSA    =  0.0000 ! Specific stem area [0..1 ha/kg, R]
  SPAN   =   33.00 ! Life span under leaves under optimum conditions  [0..366 d, R]
  TBASE  =   10.00 ! Lower threshold temperature for ageing of leaves [-10..30 C, R]

* List specific leaf area [0..1 ha/kg, R] as function of crop development stage [0..2 -, R]
*           DVS      SLA    (maximum 15 records)
  SLATB =
           0.00   0.0026
           0.78   0.0012
           2.00   0.0012
* End of table 

***********************************************************************************************

***********************************************************************************************
* Part 5: Assimilation

  KDIF   =    0.60 ! Extinction coefficient for diffuse visible light [0..2 -, R]
  KDIR   =    0.75 ! Extinction coefficient for direct visible light  [0..2 -, R]
  EFF    =    0.45 ! Light use efficiency for real leaf [0..10 kg/ha/hr/(Jm2s), R]

* List max CO2 assimilation rate [0..100 kg/ha/hr, R] as function of development stage [0..2 -, R]
*          DVS    AMAX   (maximum 15 records)
  AMAXTB =
           0.00 70.000
           1.25 70.000
           1.50 63.000
           1.75 49.000
           2.00 21.000
* End of table 

* List reduction factor of AMAX [-, R] as function of average day temperature [-10..50 C, R]
*          TAVD   TMPF  (maximum 15 records)
  TMPFTB =
           0.00  0.010
           9.00  0.050
          16.00  0.800
          18.00  0.940
          20.00  1.000
          30.00  1.000
          36.00  0.950
          42.00  0.560
* End of table 

* List reduction factor of AMAX [-, R] as function of minimum day temperature [-10..50 C, R]
*          TMNR    TMNF  (maximum 15 records)
  TMNFTB = 
           5.00  0.000
           8.00  1.000
* End of table
 
***********************************************************************************************

***********************************************************************************************
* Part 6: Conversion of assimilates into biomass

  CVL    =  0.6800 ! Efficiency of conversion into leaves         [0..1 kg/kg, R]
  CVO    =  0.6710 ! Efficiency of conversion into storage organs [0..1 kg/kg, R]
  CVR    =  0.6900 ! Efficiency of conversion into roots          [0..1 kg/kg, R]
  CVS    =  0.6580 ! Efficiency of conversion into stems          [0..1 kg/kg, R]

***********************************************************************************************

***********************************************************************************************
* Part 7: Maintenance respiration

  Q10    =  2.0000 ! Increase in respiration rate with temperature  [0..5 /10 C, R]
  RML    =  0.0300 ! Maintenance respiration rate of leaves         [0..1 kgCH2O/kg/d, R]
  RMO    =  0.0100 ! Maintenance respiration rate of storage organs [0..1 kgCH2O/kg/d, R]
  RMR    =  0.0150 ! Maintenance respiration rate of roots          [0..1 kgCH2O/kg/d, R]
  RMS    =  0.0150 ! Maintenance respiration rate of stems          [0..1 kgCH2O/kg/d, R]

* List reduction factor of senescence [-, R] as function of development stage [0..2 -, R]
*          DVS    RFSE  (maximum 15 records)
  RFSETB = 
           0.00   1.00
           1.50   1.00
           1.75   0.75
           2.00   0.25
* End of table 

***********************************************************************************************

***********************************************************************************************
* Part 8: Partitioning

* List fraction of total dry matter increase partitioned to the roots [kg/kg, R]
* as function of development stage [0..2 -, R]
*          DVS     FR    (maximum 15 records)
  FRTB = 
           0.00   0.40
           0.10   0.37
           0.20   0.34
           0.30   0.31
           0.40   0.27
           0.50   0.23
           0.60   0.19
           0.70   0.15
           0.80   0.10
           0.90   0.06
           1.00   0.00
           2.00   0.00
* End of table 

* List fraction of total above ground dry matter increase partitioned to the leaves [kg/kg, R]
* as function of development stage [0..2 -, R]
*          DVS     FL   (maximum 15 records)
  FLTB = 
           0.00   0.62
           0.33   0.62
           0.88   0.15
           0.95   0.15
           1.10   0.10   
           1.20   0.00
           2.00   0.00
* End of table 

* List fraction of total above ground dry matter increase partitioned to the stems [kg/kg, R]
* as function of development stage [0..2 -, R]
*          DVS    FS   (maximum 15 records)
  FSTB = 
           0.00   0.38
           0.33   0.38
           0.88   0.85
           0.95   0.85
           1.10   0.40
           1.20   0.00
           2.00   0.00
* End of table 

* List fraction of total above ground dry matter increase partitioned to the storage organs [kg/kg, R]
* as function of development stage [0..2 -, R]
*          DVS    FO    (maximum 15 records)
  FOTB = 
           0.00   0.00
           0.95   0.00
           1.10   0.50
           1.20   1.00
           2.00   1.00
* End of table

***********************************************************************************************

***********************************************************************************************
* Part 9: Death rates

  PERDL =   0.030 ! Maximum relative death rate of leaves due to water stress [0..3 /d, R]

* List relative death rates of roots [kg/kg/d] as function of development stage [0..2 -, R]
*          DVS    RDRR    (maximum 15 records)
  RDRRTB = 
          0.0000 0.0000
          1.5000 0.0000
          1.5001 0.0200
          2.0000 0.0200
* End of table

* List relative death rates of stems [kg/kg/d] as function of development stage [0..2 -, R]
*          DVS     RDRS    (maximum 15 records)
  RDRSTB = 
          0.0000 0.0000
          1.5000 0.0000
          1.5001 0.0200
          2.0000 0.0200
* End of table

***********************************************************************************************

***********************************************************************************************
* Part 10: Root growth and root density profile

* Switch development of root growth
  SWRD = 2         ! 1 = Root growth depends on development stage
                   ! 2 = Root growth depends on maximum daily increase
                   ! 3 = Root growth depends on available root biomass

* If case of dependency development stage (SWRD=1), specify:
* List rooting depth RD [0..1000 cm, R], as a function of development stage DVS [0..2 -, R]:

*        DVS   RD        ! (maximum MAGRS records)
  RDTB =              
        0.00   10.00
        0.30   20.00
        0.50   50.00
        0.70   80.00
        1.00   90.00
        2.00  100.00
* End of table

* If case of dependency maximum daily increase (SWRD=2), specify:
  RDI    =   10.00 ! Initial rooting depth [0..1000 cm, R]
  RRI    =    2.20 ! Maximum daily increase in rooting depth [0..100 cm/d, R]
  RDC    =  100.00 ! Maximum rooting depth of particular cultivar [0..1000 cm, R]

* Switch for calculation rooting depth:
  SWDMI2RD = 1  ! 0 = Rooting depth increase is related to availability assimilates for roots
                ! 1 = Rooting depth increase is related to relative dry matter increase

* If case of dependency available root biomass (SWRD=3), specify:
* List rooting depth RL [0..5000 cm, R] as function of root weight RW [0..5000 kg DM/ha, R]:
*            RW     RL   (maximum 11 records)
  RLWTB = 
           12.5   10.0
        1200.00  100.0
* End of table 

  WRTMAX = 3000.0    ! Maximum root weight [0..1d5 kg DM/ha, R]                

* Always specify:
* Switch for calculation of relative root density (default SWRDC = 1):
  SWRDC = 0                   ! 0 = Root density is not modified
                              ! 1 = Root density is modified based on root water extraction

* List root density [0..100 cm/cm3, R] as function of relative rooting depth [0..1 -, R]:
* In case of drought stress according to Feddes et al. (1978) (SWDROUGHT = 1), relative root density (-) is sufficient

*    Rdepth Rdensity          ! (maximum 11 records)
  RDCTB = 
          0.0  1.00
          1.0  0.00
* End of table

***********************************************************************************************                

***********************************************************************************************
* Part 11: Oxygen stress
 
* Switch for oxygen stress:
  SWOXYGEN = 2         ! 0 = No oxygen stress
                       ! 1 = Oxygen stress according to Feddes et al. (1978)
                       ! 2 = Oxygen stress according to Bartholomeus et al. (2008)

* Switch for checking aerobic conditions in root zone to stop root(zone) development
  SWWRTNONOX = 1       ! 0 = do not check for aerobic conditions
                       ! 1 = check for aerobic conditions

  AERATECRIT = 0.5     ! threshold to stop root extension in case of oxygenstress; 0.0 maximum oxygen stress [0.0001..1.0 -, R]

* If SWOXYGEN=1, specify:
  HLIM1  =     -10.0 ! No water extraction at higher pressure heads [-100..100 cm, R]
  HLIM2U =     -25.0 ! h below which optimum water extr. starts for top layer [-1000..100 cm, R]
  HLIM2L =     -25.0 ! h below which optimum water extr. starts for sub layer [-1000..100 cm, R]

* If SWOXYGEN=2, specify:
  Q10_MICROBIAL       = 2.8d0          ! Relative increase in microbial respiration at temperature increase of 10 C [1.0..4.0 -, R]
  SPECIFIC_RESP_HUMUS = 1.6d-3         ! Respiration rate of humus at 25 C [0.0..1.0 kg O2/kg C/d, R] 
  SRL                 = 151375.d0  ! Specific root length [0.0..1.d10 m root/kg root, R]      
  SWROOTRADIUS        = 2              ! Switch for calculation of root radius:
                                       ! 1 = Calculate root radius
                                       ! 2 = Root radius is given in input file
* If SWROOTRADIUS=1, specify:
  DRY_MAT_CONT_ROOTS      = 0.075d0    ! Dry matter content of roots [0.0..1.0 -, R]
  AIR_FILLED_ROOT_POR     = 0.05d0     ! Air filled root porosity [0.0..1.0 -, R]
  SPEC_WEIGHT_ROOT_TISSUE = 1.0d3      ! Specific weight of non-airfilled root tissue [0.0..1.d5 kg root/m3 root, R]
  VAR_A                   = 4.175d-10  ! Variance of root radius [0.0..1.0 -, R]

* If SWROOTRADIUS=2, specify:
  ROOT_RADIUSO2 = 0.00015d0            ! Root radius (mind: in meter!) for oxygen stress module [1d-6..0.1 m, R]

***********************************************************************************************                

***********************************************************************************************
* Part 12: Drought stress
  
* Switch for drought stress:
  SWDROUGHT = 1         ! 1 = Drought stress according to Feddes et al. (1978)
                        ! 2 = Drought stress according to De Jong van Lier et al. (2008)

* If SWDROUGHT=1, or in case of irrigation scheduling (SCHEDULE = 1), specify:
  HLIM3H =    -400.0    ! Pressure head below which water uptake reduction starts at high Tpot [-1d4..100 cm, R]
  HLIM3L =    -500.0    ! Pressure head below which water uptake reduction starts at low Tpot  [-1d4..100 cm, R]
  HLIM4  =  -10000.0    ! No water extraction at lower soil water pressure heads [-1.6d4..100 cm, R]
  ADCRH  =       0.5    ! Level of high atmospheric demand, corresponding to HLIM3H [0..5 cm/d, R]     
  ADCRL  =       0.1    ! Level of low atmospheric demand, corresponding to HLIM3L [0..5 cm/d, R]     

* If SWDROUGHT=2, specify:
  WILTPOINT  = -20000.0 ! Minimum pressure head in leaves [-1d8..-1d2 cm, R]
  KSTEM =       1.03d-4 ! Hydraulic conductance between leaf and root xylem [1d-10..10 /d, R]
  RXYLEM =         0.02 ! Xylem radius [1d-4..1 cm, R]
  ROOTRADIUS =     0.05 ! Root radius [1d-4..1 cm, R]
  KROOT =        3.5d-5 ! Radial hydraulic conductivity of root tissue [1d-10..1d10 cm/d, R] 
  ROOTCOEFA  =     0.53 ! Defines relative distance between roots at which mean soil water content occurs [0..1 -, R]
  SWHYDRLIFT =        0 ! Switch for possibility hydraulic lift in root system [N=0, Y=1]
  ROOTEFF    =      1.0 ! Root system efficiency factor [0..1 -, R]
  STEPHR   =        1.0 ! Step between values of hroot and hxylem in iteration cycle [0..10 cm, R]
  CRITERHR =      0.001 ! Maximum difference of Hroot between iterations; convergence criterium [0..10 cm, R]
  TACCUR =        0.001 ! Maximum absolute difference between simulated and calculated potential transpiration rate (1d-5..1d-2 cm/d, R)

***********************************************************************************************

***********************************************************************************************
* Part 13: salt stress                                            

* Switch salinity stress 
  SWSALINITY = 1              ! 0 = No salinity stress
                              ! 1 = Maas and Hoffman reduction function
                              ! 2 = Use osmotic head

* If SWSALINITY = 1, specify threshold and slope of Maas and Hoffman
  SALTMAX   =  0.82           ! Threshold salt concentration in soil water  [0..100 mg/cm3, R] 
  SALTSLOPE =  0.070          ! Decline of root water uptake above threshold [0..1.0 cm3/mg, R] 

***********************************************************************************************

***********************************************************************************************
* Part xx: compensation of root water uptake stress

* Switch for compensation root water uptake stress
  SWCOMPENSATE = 2           ! 0 = No compensation
                             ! 1 = Compensation according to Jarvis (2011)
                             ! 2 = Compensation according to Walsum (2019)

* Switch for selection of stressors to compensate
  SWSTRESSOR = 3             ! 1 = Compensation of all stressors
                             ! 2 = Compensation of drought stress
                             ! 3 = Compensation of oxygen stress
                             ! 4 = Compensation of salinity stress
                             ! 5 = Compensation of frost stress

* If SWCOMPENSATE = 1, specify:
  ALPHACRIT = 0.7            ! Critical stress index for compensation of root water uptake [0.2..1 -, R]

* If SWCOMPENSATE = 2, specify:
  DCRITRTZ = 5.0             ! Threshold of rootzone thickness after which compensation occurs [0.02..100 cm, R]

***********************************************************************************************

***********************************************************************************************
* Part 14: interception                                            

* For agricultural crops apply interception concept of Von Hoyningen-Hune and Braden
  SWINTER =  1  ! Switch for rainfall interception method:
                ! 0 = No interception calculated
                ! 1 = Agricultural crops (Von Hoyningen-Hune and Braden)
                ! 2 = Trees and forests (Gash)

* In case of agricultural crops (SWINTER=1) specify:
  COFAB =  0.50 ! Interception coefficient, corresponding to maximum interception amount [0..1 cm, R]
  
***********************************************************************************************

***********************************************************************************************
* Part 15: CO2-impact
*
* CO2-impact:
*    correction of photosynthesis as a function of atmospheric CO2 concentration (-)
*    correction of radiation use efficiency as a function of atmospheric CO2 concentration (-)
*    correction of transpiration as a function of atmospheric CO2 concentration (-)
*    values for C4 crops (forage maize)
*    actual CO2 concentration in atmosphere [ppm] in separate file atmospheric.co2
***********************************************************************************************

* Switch for assimilation correction due to CO2 impact
  SWCO2 =  1    ! 0 = No CO2 assimilation correction
                ! 1 = CO2 assimilation correction

* if SWCO2=1, specify:
  ATMOFIL = 'atmospheric'

  CO2AMAXTB  =  
              40.0  0.00
             360.0  1.00
             720.0  1.00
            1000.0  1.00
            2000.0  1.00
* End of table
   
  CO2EFFTB   =  
              40.0  0.00
             360.0  1.00
             720.0  1.00
            1000.0  1.00
            2000.0  1.00
* End of table
   
  CO2TRATB   =  
              40.0  0.00
             360.0  1.00
             720.0  0.74 
            1000.0  0.74
            2000.0  0.74
* End of table

***********************************************************************************************

*** MANAGEMENT SECTION ***

***********************************************************************************************
* Part 1: Nitrogen use
*
* Data from: Linutl4,  http://models.pps.wur.nl/models
*                      param values from MAG202.DATo
*      reference:    Wolf, J. (2012). Users guide for LINTUL4 and LINTUL4V: 
*                    Simple generic model for simulation of crop growth under 
*                    potential, water limited and nitrogen limited conditions. 
*                    WUR-PPS report (Vol. 4).
*   RDRNS    =   ! max. relative death rate of leaves due to N stress
*   DVSNLT   =   ! development stage above which no crop nitrogen uptake does occur
*   DVSNT    =   ! development stage above which nitrogen translocation to storage organs does occur 
*   FNTRT    =   ! nitrogen translocation from roots as a fraction of total N amount translocated from leaves and stems
*   FRNX     =   ! optimal N concentration as fraction of maximum N concentration
*   LRNR     =   ! maximum N concentration in roots as fraction of maximum N concentration in leaves
*   LSNR     =   ! maximum N concentration in stems as fraction of maximum N concentration in leaves
*   NLAI     =   ! coefficient for the reduction due to N stress of the LAI increase (during juvenile phase)
*   NLUE     =   ! coefficient for the reduction of RUE due to  Nitrogen stress
*   NMAXSO   =   ! maximum N concentration (= 1.6*min. N conc.) in storage organs [kg N kg-1 dry biomass]
*   NPART    =   ! coefficient for the effect of N stress on leaf biomass reduction 
*   NSLA     =   ! coefficient for the effect of N stress on SLA reduction
*   RNFLV    =   ! residual N fraction in leaves [kg N kg-1 dry biomass]
*   RNFST    =   ! residual N fraction in stems [kg N kg-1 dry biomass]
*   RNFRT    =   ! residual N fraction in roots [kg N kg-1 dry biomass]
*   TCNT     =   ! time coefficient for N translocation to storage organs [days]
*   NFIXF    =   ! fraction of crop nitrogen uptake by biological fixation [-]
*
* Maximum N concentration in leaves as function of development stage [kg N kg-1 dry biomass]
*  NMXLV    =  
*
* End of table

***********************************************************************************************

***********************************************************************************************
* Part 2: Losses of organic matter

* Harvest losses of organic matter 
  FRAHARLOSORM_LV     = 0.20 ! fraction harvest losses of organic matter from leaves         [0.0..1.0 kg.kg-1 DM, R]
  FRAHARLOSORM_ST     = 0.10 ! fraction harvest losses of organic matter from stems          [0.0..1.0 kg.kg-1 DM, R]
  FRAHARLOSORM_SO     = 0.01 ! fraction harvest losses of organic matter from storage organs [0.0..1.0 kg.kg-1 DM, R]

* Losses of organic matter 
  FRADECEASEDLVTOSOIL = 0.30 ! Fraction of deceased leaves incorporated in soil  [0..1.0 kg/kg DM, R]

***********************************************************************************************

***********************************************************************************************
* Part 3: Management, other than irrigation, for instance pests,diseases or nutrients
 
* Switch for calculation of potential yield
  SWPOTRELMF = 2      ! 1 = theoretical potential yield
                      ! 2 = attainable yield

* In case of pest, diseases or nutrients, specify:
  RELMF = 0.90        ! Relative management factor to reduce theoretical potential yield to attainable yield [0..1 -, R]

***********************************************************************************************

*** IRRIGATION SCHEDULING SECTION ***

***********************************************************************************************
* Part 1: General

  SCHEDULE = 0                ! Switch for application irrigation scheduling [Y=1, N=0] 

***********************************************************************************************

* If SCHEDULE = 1, specify:
  STARTIRR = 16 04            ! Specify day and month at which irrigation scheduling starts [dd mm]
  ENDIRR  =  31 10            ! Specify day and month at which irrigation scheduling stops [dd mm]
  CIRRS = 0.0                 ! Solute concentration of irrigation water [0..100 mg/cm3, R]

* Switch for type of irrigation method: 
  ISUAS = 0                   ! 0 = sprinkling irrigation
                              ! 1 = surface irrigation

**********************************************************************************

**********************************************************************************
* Part 2: Irrigation time criteria

* Choose one of the following timing criteria options [1..6 -, I]:
  TCS = 1                     ! 1 = Ratio actual/potential transpiration
                              ! 2 = Depletion of Readily Available Water
                              ! 3 = Depletion of Totally Available Water
                              ! 4 = Depletion of absolute Water Amount
                              ! 6 = Fixed weekly irrigation
                              ! 7 = Pressure head
                              ! 8 = Moisture content

* Ratio actual/potential transpiration (TCS = 1)
* Specify mimimum of ratio actual/potential transpiration TREL [0..1 -, R] as function of crop development stage

  DVS_TC1  TREL
      0.0  0.85
      2.0  0.85
* End of table

* Switch for minimum time interval between irrigation applications
  TCSFIX = 1                  ! 0 = No minimum time interval
                              ! 1 = Define minimum time interval

* If TCSFIX = 1, specify:
  IRGDAYFIX = 7               ! Minimum number of days between irrigation applications [1..366 d, I]

**********************************************************************************

**********************************************************************************
* Part 3: Irrigation depth criteria

* Choose one of the following two options for irrigation depth:
  DCS = 2                     ! 1 = Back to field capacity
                              ! 2 = Fixed Irrigation Depth

* Specify fixed irrigation depth FID [0..400 mm, R] as function of crop development stage [0..2, R]:
    DVS_DC2   FID
        0.0  20.0
        2.0  20.0
* End of table

* Select minimum and maximum of irrigation depths:
  DCSLIM = 0                  ! Switch, limit range irrigation depth  [Y=1, N=0]

**********************************************************************************

* End of .crp file !
