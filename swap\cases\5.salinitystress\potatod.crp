**********************************************************************************
* Contents: SWAP 4 - Crop data (WOFOST)
**********************************************************************************
* Comment area:
* Testbank of SWAP: build with template of crp-file
*
**********************************************************************************

*** PLANT GROWTH SECTION ***

**********************************************************************************
* Part 0 : Preparation, Sowing, Germination and Harvest

* Part 0a: Preparation before crop growth

* Switch for preparation:
  SWPREP = 0                 ! 0 = No preparation
                              ! 1 = Preparation before start of crop growth

* Part 0b: Sowing
* Switch for sowing:
  SWSOW = 0                  ! 0 = No sowing
                              ! 1 = Sowing before start of crop growth

* Part 0c: Germination
  
* Switch for germination:
  SWGERM = 0                 ! 0 = No germination
                              ! 1 = Simulate germination depending on temperature
                              ! 2 = Simulate germination depending on temperature and hydrological conditions

* Part 0d: Harvest

  DVSEND = 3.0               ! Development stage at harvest [0..3 -, R]
  
* Switch for timing of harvest:
  SWHARV = 0                 ! 0 = Timing of harvest depends on end of growing period (CROPEND)
                              ! 1 = Timing of harvest depends on development stage (DVSEND)

**********************************************************************************

**********************************************************************************
* Part 1: crop factor or crop height

* Choose between crop factor and crop height
* Choose crop factor if ETref is used, either from meteo input file (SWETR = 1) or with Penman-Monteith
* Choose crop height if Penman-Monteith should be used with actual crop height, albedo and canopy resistance
  SWCF = 2                   ! 1 = Crop factor 
                              ! 2 = Crop height

* If SWCF = 2, list Crop Height [0..1.d4 cm, R], as function of dev. stage [0..2 -, R]:

 DVS    CH
 0.0   1.0
 1.0  40.0
 2.0  50.0
* End of table

* If SWCF = 2, in addition to crop height list crop specific values for:
  ALBEDO = 0.19              ! Crop reflection coefficient [0..1.0 -, R]                    
  RSC = 207.0                ! Minimum canopy resistance [0..1d6 s/m, R]
  RSW = 0.0                  ! Canopy resistance of intercepted water [0..1d6 s/m, R]

**********************************************************************************

**********************************************************************************
* Part 2 : Crop development

* Switch for crop development:
  IDSL = 0                   ! 0 = Crop development before anthesis depends on temperature
                             ! 1 = Crop development before anthesis depends on temperature and daylength
                             ! 2 = Crop development before anthesis depends on temperature, daylength and vernalisation factor

* Specify temperature dependency:
  TSUMEA = 150.0             ! Temperature sum from emergence to anthesis [0..10000 degree C, R]
  TSUMAM = 1550.0            ! Temperature sum from anthesis to maturity  [0..10000 degree C, R]

* List increase in temperature sum [0..60 degree C, R] as function of daily average temperature [0..100 degree C, R]
*   TAV  DTSM    (maximum 15 records)
  DTSMTB =
0.0 0.0
2.0 0.0
13.0 11.0
30.0 28.0
* End of table


**********************************************************************************

**********************************************************************************
* Part 3: Initial values

  TDWI = 75.0                ! Initial total crop dry weight [0..10000 kg/ha, R]
  LAIEM = 0.0589             ! Leaf area index at emergence [0..10 m2/m2, R]
  RGRLAI = 0.012             ! Maximum relative increase in LAI [0..1 m2/m2/d, R]

**********************************************************************************

**********************************************************************************
* Part 4: Green surface area

  SPA = 0.0                  ! Specific pod area  [0..1 ha/kg, R]
  SSA = 0.0                  ! Specific stem area [0..1 ha/kg, R]
  SPAN = 37.0                ! Life span under leaves under optimum conditions  [0..366 d, R]
  TBASE = 2.0                ! Lower threshold temperature for ageing of leaves [-10..30 degree C, R]

* List specific leaf area [0..1 ha/kg, R] as function of crop development stage [0..2 -, R]
*   DVS     SLA    (maximum 15 records)
  SLATB =
0.0 0.0030
1.1 0.0030
2.0 0.0015
* End of table 

**********************************************************************************

**********************************************************************************
* Part 5: Assimilation

  KDIF = 1.0                 ! Extinction coefficient for diffuse visible light [0..2 -, R]
  KDIR = 0.75                ! Extinction coefficient for direct visible light  [0..2 -, R]
  EFF = 0.45                 ! Light use efficiency for real leaf [0..10 kg/ha/hr/(Jm2s), R]

* List maximum CO2 assimilation rate [0..100 kg/ha/hr, R] as function of development stage [0..2 -, R]
*   DVS    AMAX   (maximum 15 records)
  AMAXTB =
0.00 30.0
1.57 30.0
2.00 0.0
* End of table 

* List reduction factor of AMAX [-, R] as function of average day temperature [-10..50 degree C, R]
*   TAVD   TMPF  (maximum 15 records)
  TMPFTB =
0.0 0.01
3.0 0.01
10.0 0.75
15.0 1.00
20.0 1.00
26.0 0.75
33.0 0.01
* End of table 

* List reduction factor of AMAX [-, R] as function of minimum day temperature [-10..50 degree C, R]
*   TMNR    TMNF  (maximum 15 records)
  TMNFTB =
0.0 0.0
3.0 1.0
* End of table 

**********************************************************************************

**********************************************************************************
* Part 6: Conversion of assimilates into biomass

  CVL = 0.72                 ! Efficiency of conversion into leaves [0..1 kg/kg, R]
  CVO = 0.85                 ! Efficiency of conversion into storage organs [0..1 kg/kg, R]
  CVR = 0.72                 ! Efficiency of conversion into roots [0..1 kg/kg, R]
  CVS = 0.69                 ! Efficiency of conversion into stems [0..1 kg/kg, R]

**********************************************************************************

**********************************************************************************
* Part 7: Maintenance respiration

  Q10 = 2.0                  ! Increase in respiration rate with temperature  [0..5 /10 degree C, R]
  RML = 0.03                 ! Maintenance respiration rate of leaves [0..1 kgCH2O/kg/d, R]
  RMO = 0.0045               ! Maintenance respiration rate of storage organs [0..1 kgCH2O/kg/d, R]
  RMR = 0.01                 ! Maintenance respiration rate of roots [0..1 kgCH2O/kg/d, R]
  RMS = 0.015                ! Maintenance respiration rate of stems [0..1 kgCH2O/kg/d, R]

* List reduction factor of senescence [-, R] as function of development stage [0..2 -, R]
*   DVS    RFSE  (maximum 15 records)
  RFSETB = 
0.0 1.0
2.0 1.0
* End of table 

**********************************************************************************

**********************************************************************************
* Part 8: Partitioning

* List fraction of total dry matter increase partitioned to the roots [kg/kg, R]
* as function of development stage [0..2 -, R]
*   DVS     FR    (maximum 15 records)
  FRTB = 
0.00 0.2
1.00 0.2
1.27 0.0
1.36 0.0
2.00 0.0
* End of table 

* List fraction of total above ground dry matter increase partitioned to the leaves [kg/kg, R]
* as function of development stage [0..2 -, R]
*   DVS     FL   (maximum 15 records)
  FLTB = 
0.00 0.8
1.00 0.8
1.27 0.0
1.36 0.0
2.00 0.0
* End of table 

* List fraction of total above ground dry matter increase partitioned to the stems [kg/kg, R]
* as function of development stage [0..2 -, R]
*   DVS    FS   (maximum 15 records)
  FSTB = 
0.00 0.20
1.00 0.20
1.27 0.25
1.36 0.00
2.00 0.00
* End of table 

* List fraction of total above ground dry matter increase partitioned to the storage organs [kg/kg, R]
* as function of development stage [0..2 -, R]
*   DVS    FO    (maximum 15 records)
  FOTB = 
0.00 0.00
1.00 0.00
1.27 0.75
1.36 1.00
2.00 1.00
* End of table

**********************************************************************************

**********************************************************************************
* Part 9: Death rates

  PERDL = 0.03               ! Maximum relative death rate of leaves due to water stress [0..3 /d, R]

* List relative death rates of roots [kg/kg/d] as function of development stage [0..2 -, R]
*   DVS    RDRR    (maximum 15 records)
  RDRRTB = 
0.0000 0.00
1.5000 0.00
1.5001 0.02
2.0000 0.02
* End of table

* List relative death rates of stems [kg/kg/d] as function of development stage [0..2 -, R]
*   DVS     RDRS    (maximum 15 records)
  RDRSTB = 
0.0000 0.00
1.5000 0.00
1.5001 0.02
2.0000 0.02
* End of table

**********************************************************************************

**********************************************************************************
* Part 10: Root density profile

* Switch development of root depth
  SWRD = 2                   ! 1 = Root depth depends on development stage
                             ! 2 = Root depth depends on maximum daily increase
                             ! 3 = Root depth depends on available root biomass

* If case of dependency maximum daily increase (SWRD = 2), specify:
  RDI = 10.0                 ! Initial rooting depth [0..1000 cm, R]
  RRI = 1.2                  ! Maximum daily increase in rooting depth [0..100 cm/d, R]
  RDC = 50.0                 ! Maximum rooting depth of particular crop [0..1000 cm, R]

* Switch for calculation rooting depth:
  SWDMI2RD = 1               ! 0 = Rooting depth increase is related to availability assimilates for roots
                             ! 1 = Rooting depth increase is related to relative dry matter increase


* Always specify:
* List root density [0..100 cm/cm3, R] as function of relative rooting depth [0..1 -, R]:
* In case of drought stress according to Feddes et al. (1978) (SWDROUGHT = 1), relative root density (-) is sufficient

*   RRD    RDENS
  RDCTB =
0.0 1.0
1.0 0.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 11: Oxygen stress

* Switch for oxygen stress:
  SWOXYGEN = 1               ! 0 = No oxygen stress
                             ! 1 = Oxygen stress according to Feddes et al. (1978)
                             ! 2 = Oxygen stress according to Bartholomeus et al. (2008)

* Switch for checking aerobic conditions in root zone to stop root(zone) development
  SWWRTNONOX = 1             ! 0 = Do not check for aerobic conditions
                             ! 1 = Check for aerobic conditions

  AERATECRIT = 0.5           ! Threshold to stop root extension in case of oxygenstress; 0.0 maximum oxygen stress [0.0001..1.0 -, R]

* If SWOXYGEN=1, specify:
  HLIM1 = -10.0              ! No water extraction at higher pressure heads [-100..100 cm, R]
  HLIM2U = -25.0             ! H below which optimum water extraction starts for top layer [-1000..100 cm, R]
  HLIM2L = -25.0             ! H below which optimum water extraction starts for sub layer [-1000..100 cm, R]

**********************************************************************************

**********************************************************************************
* Part 12: Drought stress
  
* Switch for drought stress:
  SWDROUGHT = 1              ! 1 = Drought stress according to Feddes et al. (1978)
                             ! 2 = Drought stress according to De Jong van Lier et al. (2008)

* If SWDROUGHT=1, or in case of irrigation scheduling (SCHEDULE = 1), specify:
  HLIM3H = -300.0            ! Pressure head below which water uptake reduction starts at high Tpot [-1d4..100 cm, R]
  HLIM3L = -500.0            ! Pressure head below which water uptake reduction starts at low Tpot  [-1d4..100 cm, R]
  HLIM4 = -10000.0           ! No water extraction at lower soil water pressure heads [-1.6d4..100 cm, R]
  ADCRH = 0.5                ! Level of high atmospheric demand, corresponding to HLIM3H [0..5 cm/d, R]     
  ADCRL = 0.1                ! Level of low atmospheric demand, corresponding to HLIM3L [0..5 cm/d, R]     

**********************************************************************************

**********************************************************************************
* Part 13: salt stress

* Switch salinity stress
  SWSALINITY = 1             ! 0 = No salinity stress
                             ! 1 = Maas and Hoffman reduction function
                             ! 2 = Use osmotic head

* If SWSALINITY = 1, specify threshold and slope of Maas and Hoffman
  SALTMAX = 0.732            ! Threshold salt concentration in soil water  [0..100 mg/cm3, R] 
  SALTSLOPE = 0.0868         ! Decline of root water uptake above threshold [0..1.0 cm3/mg, R] 

**********************************************************************************

**********************************************************************************
* Part xx: compensation of root water uptake stress

* Switch for compensation root water uptake stress
  SWCOMPENSATE = 0           ! 0 = No compensation
                             ! 1 = Compensation according to Jarvis (1989)
                             ! 2 = Compensation according to Walsum (2019)

**********************************************************************************

**********************************************************************************
* Part 14: interception                                            

* For agricultural crops apply interception concept of Von Hoyningen-Hune and Braden
* Switch for rainfall interception method:
  SWINTER = 1                ! 0 = No interception calculated
                             ! 1 = Agricultural crops (Von Hoyningen-Hune and Braden)
                             ! 2 = Trees and forests (Gash)

* In case of agricultural crops (SWINTER = 1) specify:
  COFAB = 0.25               ! Interception coefficient, corresponding to maximum interception amount [0..1 cm, R]

**********************************************************************************

**********************************************************************************
* Part 15: CO2-impact
*
* CO2-impact:
*    correction of photosynthesis as a function of atmospheric CO2 concentration (-)
*    correction of radiation use efficiency as a function of atmospheric CO2 concentration (-)
*    correction of transpiration as a function of atmospheric CO2 concentration (-)
*    values for C3 crops (potatoes, grassland, soybean)
*    actual CO2 concentration in atmosphere [ppm] in separate file atmospheric.co2
**********************************************************************************

* Switch for assimilation correction due to CO2 impact
  SWCO2 = 0                  ! 0 = No CO2 assimilation correction
                             ! 1 = CO2 assimilation correction

**********************************************************************************

*** MANAGEMENT SECTION ***

**********************************************************************************
* Part 1: Nitrogen use

**********************************************************************************

**********************************************************************************
* Part 2: Losses of organic matter

* Harvest losses of organic matter 
  FRAHARLOSORM_LV = 0.2      ! Fraction harvest losses of organic matter from leaves [0.0..1.0 kg kg-1, R]
  FRAHARLOSORM_ST = 0.1      ! Fraction harvest losses of organic matter from stems [0.0..1.0 kg kg-1, R]
  FRAHARLOSORM_SO = 0.01     ! Fraction harvest losses of organic matter from storage organs [0.0..1.0 kg kg-1, R]

* Losses of organic matter 
  FRADECEASEDLVTOSOIL = 0.3  ! Fraction of deceased leaves incorporated in soil  [0..1.0 kg kg-1, R]

**********************************************************************************

**********************************************************************************
* Part 3: Management, other than irrigation, for instance pests,diseases or nutrients
 
* Switch for calculation of potential yield
  SWPOTRELMF = 2             ! 1 = Theoretical potential yield
                             ! 2 = Attainable yield

* In case of pest, diseases or nutrients, specify:
  RELMF = 0.8                ! Relative management factor to reduce theoretical potential yield to attainable yield [0..1 -, R]

**********************************************************************************

*** IRRIGATION SCHEDULING SECTION ***

**********************************************************************************
* Part 1: General

  SCHEDULE = 0               ! Switch for application irrigation scheduling [Y=1, N=0] 

**********************************************************************************

* End of .crp file           !
