#!/bin/bash
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c variables.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c swap_csv_output.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c sptabulated.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_declarations.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_interface.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c WC_K_models_04_11.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c tillage.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofostnut.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c tridag.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c checkmassbal.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_watern.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c fluxes.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c calcgwl.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c hysteresis.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c penmon.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c divdra.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c sharedsimulation.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c watstor.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c sharedexchange.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c convertdiscrvert.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c meteoday.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c oxygenstress.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c irrigation.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c rootextraction.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c frozencond.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c solute.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c integral.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_cropresidues.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c readswap.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c timecontrol.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c temperature.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c management_soil.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_rateconstants.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c soilwater.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c initialize.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_balancecheck.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c readmeteo.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c drainage.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c swapoutput.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c macrorate.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c calcgrid.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_parameters.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c snow.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c boundtop.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_amendments.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c swap.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c macroporeoutput.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c wofost_soil_orgmatn.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c headcalc.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c boundbottom.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c cropgrowth.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c surfacewater.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c macropore.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c functions.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c meteodt.f90
ifort -diag-file-append=diagnostics.diag -nologo -O2 -m64 -fpscomp general -warn declarations -warn unused -warn uncalled -warn interfaces -save -init=zero -fpe0 -fp-model source -fp-speculation=off -module=x64\release\ -object=x64\release\ -static -threads -c swap_main.f90
ifort -o swap *.o TTutil427.a
