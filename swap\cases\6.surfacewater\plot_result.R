# Wageningen Environmental Research
# <EMAIL>
#------------------------------------------------

# set start of program
TIMPRGSTART <- Sys.time()

# set modus
test <- FALSE

# read arguments
#------------------------------------------------

if (!test) {
   command_args <- commandArgs(trailingOnly = TRUE)
   if (length(command_args) != 0) {
     warning("ERROR: wrong usage of script")
     warning("Arguments: none")
     stop()
    }
  } else {
   command_args <- NULL
   setwd("d:/cases/6.surfacewater")
  }
ctrl <- command_args[1]

# load libraries
#------------------------------------------------
source("../../Rsoftware/libraries.R")

# run R-script
#------------------------------------------------
message(str_c("\nProgram R-PROG started...\n"))

# ---- initial part of procedure ----

file_gwl <- "./observed/gwl.csv"
file_swp <- "./swap.swp"

# ---- main part of procedure ----

# load observed groundwater level
db_gwl <- read_csv(file = file_gwl, col_types = "Dd", lazy = FALSE, comment = "#") %>%
  select(date, value) %>%
  rename(observed = value)

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output.csv")
db_swp <- read_csv_SWAP(file = file_csv, variable = c("GWL")) %>%
  mutate(
    date = as_date(datetime),
    modelled = GWL
  ) %>%
  select(date, modelled)

# extract weir level
DRFIL <- get_value_SWAP(variable = "DRFIL", file = file_swp)
file_dra <- str_c(DRFIL, ".dra")
IMPEND <- get_value_SWAP(variable = "MANSECWATLVL::IMPEND", file = file_dra)
HBWEIR <- get_value_SWAP(variable = "QWEIR::HBWEIR", file = file_dra)
db_weir <- NULL
for (rec in 2:length(IMPEND)) {
  db_tmp <- tibble(date_start = IMPEND[rec-1], date_end = IMPEND[rec], weirlevel = HBWEIR[rec])
  db_weir <- rbind(db_weir, db_tmp)
}

# combine data
db_fig <- left_join(x = db_swp, y = db_gwl, by = "date")

# set limits
ylim <- c(floor(min(c(db_fig$modelled, db_fig$observed), na.rm = TRUE) / 50) * 50, 0)
xpos <- min(db_fig$date)
ypos <- ylim[1] + 0.05 * (ylim[2] - ylim[1])

# set performance
db_prf <- get_modelperformance(actual = db_fig$observed / 100, predicted = db_fig$modelled / 100, performance = c("rmse", "rpearson")) %>%
  mutate(
    xpos = xpos,
    ypos = ypos,
    label = str_c("RMSE: ", formatC(x = rmse, format = "f", digits = 2), "  Rpearson: ", formatC(x = rpearson, format = "f", digits = 2))
  )

# create plot
G <- ggplot(data = db_fig) +
  geom_rect(data = db_weir, aes(xmin = date_start, xmax = date_end, ymin = ylim[1] / 100, ymax = weirlevel / 100), fill = "lightblue", alpha = 0.6) +
  geom_line(aes(x = date, y = modelled / 100), colour = "blue") +
  geom_point(data = db_gwl, aes(x = date, y = observed / 100), colour = "red") +
  geom_text(data = db_prf, aes(x = xpos, y = ypos / 100, label = label), colour = "black", hjust = "inward", fontface = "italic") +
  coord_cartesian(ylim = ylim / 100) +
  labs(x = "", y = create_label(label = "Groundwater level", unit = "m + MSL")) +
  get_my_theme("figure")

ggsave(filename = str_c(OUTFIL, ".png"), plot = G, width = 10, height = 5, dpi = "retina", bg = "white")

# ---- return part of procedure ----

TIMPRGEND <- Sys.time()
TIMPRGCALC <- as.numeric(difftime(time1 = TIMPRGEND, time2 = TIMPRGSTART, units = "secs"))
message(paste0("\nProgram R-PROG successfully ended in ", floor(TIMPRGCALC / 60), " minutes and ", ceiling(TIMPRGCALC %% 60), " seconds"))
q(save = "no")
