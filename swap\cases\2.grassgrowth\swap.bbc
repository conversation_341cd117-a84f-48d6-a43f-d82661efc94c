**********************************************************************************
* Filename: swap.bbc
* Contents: SWAP 4 - Main input data
**********************************************************************************
* Comment area:
* Testbank of SWAP: build with template of bbc-file
*
**********************************************************************************

*** BOTTOM BOUNDARY SECTION ***

**********************************************************************************
* Bottom boundary condition

* Select one of the following options:
  SWBOTB = 1                 ! 1  Prescribe groundwater level
                             ! 2  Prescribe bottom flux
                             ! 3  Calculate bottom flux from hydraulic head of deep aquifer
                             ! 4  Calculate bottom flux as function of groundwater level
                             ! 5  Prescribe soil water pressure head of bottom compartment
                             ! 6  Bottom flux equals zero
                             ! 7  Free drainage of soil profile
                             ! 8  Free outflow at soil-air interface

* Options 1-5 require additional bottom boundary data below

**********************************************************************************

**********************************************************************************
* In case of SWBOTB = 1, prescribe groundwater level

* specify DATE1 [YYYY-MM-DD] and GWLEVEL [cm, -10000..1000, R]:
      DATE1  GWLEVEL
 1980-04-24    -88.0
 1980-05-02   -121.0
 1980-05-07    -98.0
 1980-05-21   -121.0
 1980-05-28   -131.0
 1980-06-06   -135.0
 1980-06-11   -141.0
 1980-06-27   -136.0
 1980-07-09    -99.0
 1980-07-25    -63.0
 1980-08-08   -102.0
 1980-08-20   -104.0
 1980-09-04   -102.0
 1980-09-19    -85.0
 1980-10-03   -108.0
 1980-10-14    -98.0
 1980-10-28    -87.0
 1981-01-24    -34.0
 1981-03-03    -69.0
 1981-03-24    -60.0
 1981-04-07    -81.0
 1981-04-13    -90.0
 1981-04-21    -97.0
 1981-04-27   -102.0
 1981-05-07    -87.0
 1981-05-13   -109.0
 1981-05-19   -110.0
 1981-05-26   -112.0
 1981-06-02   -103.0
 1981-06-09   -106.0
 1981-06-12   -114.0
 1981-06-16   -120.0
 1981-06-24   -128.0
 1981-06-30    -69.0
 1981-07-07    -94.0
 1981-07-09   -102.0
 1981-07-15    -89.0
 1981-07-21   -100.0
 1981-07-28    -73.0
 1981-08-04    -96.0
 1981-08-11   -108.0
 1981-08-18   -125.0
 1981-08-25   -131.0
 1981-08-31   -138.0
 1981-09-01   -138.0
 1981-09-03   -142.0
 1981-09-08   -147.0
 1981-09-15   -147.0
 1981-09-22   -134.0
 1981-09-30   -135.0
 1981-10-07   -129.0
 1981-10-14    -87.0
 1981-10-21    -70.0
 1981-10-28    -80.0
 1981-11-06    -74.0
 1981-11-12    -54.0
 1981-11-19    -56.0
 1981-11-26    -45.0
 1981-12-02    -41.0
 1982-01-05    -30.0
 1982-03-26    -84.0
 1982-04-07    -89.0
 1982-04-14    -90.0
 1982-04-28   -106.0
 1982-05-11    -91.0
 1982-05-19   -104.0
 1982-05-26   -109.0
 1982-06-02   -119.0
 1982-06-09   -124.0
 1982-06-16   -124.0
 1982-06-22   -120.0
 1982-06-30   -103.0
 1982-07-06   -100.0
 1982-07-13   -120.0
 1982-07-22   -144.0
 1982-07-28   -153.0
 1982-08-11   -141.0
 1982-08-18   -146.0
 1982-08-25   -149.0
 1982-09-03   -155.0
 1982-09-08   -158.0
 1982-09-22   -165.0
 1982-10-01   -168.0
 1982-10-14   -155.0
 1982-10-20   -140.0
 1982-11-01   -133.0
 1982-11-19    -90.0
 1982-11-26    -73.0
 1983-03-29    -51.0
 1983-04-08    -56.0
 1983-04-15    -69.0
 1983-04-28    -76.0
 1983-05-10    -64.0
 1983-05-18    -54.0
 1983-05-31    -51.0
 1983-06-09    -77.0
 1983-06-20   -104.0
 1983-06-30    -95.0
 1983-07-14   -126.0
 1983-07-26   -141.0
 1983-08-11   -155.0
 1983-08-25   -170.0
 1983-09-08   -180.0
 1983-09-21   -140.0
 1983-09-28   -140.0
 1983-10-13   -143.0
 1983-10-28   -138.0
 1983-11-18   -146.0
 1984-03-14    -84.0
 1984-03-22    -88.0
 1984-04-10    -69.0
 1984-05-10    -88.0
 1984-05-23    -53.0
 1984-06-13    -81.0
 1984-07-02   -100.0
 1984-07-09   -105.0
 1984-07-19   -102.0
 1984-08-01   -122.0
 1984-08-16   -132.0
 1984-08-30   -146.0
 1984-09-26   -109.0
 1984-10-03    -70.0
 1984-10-16    -56.0
 1984-11-01    -50.0
 1984-11-27    -55.0
 1984-12-12    -86.0
* End of table

**********************************************************************************

* End of input file .BBC     !
