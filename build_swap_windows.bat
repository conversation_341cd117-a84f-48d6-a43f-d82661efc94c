@echo off
REM ========================================================================
REM SWAP 4.2.0 Windows Build Script
REM Based on Intel Fortran Compiler settings from compiler_settings_4.2.0.txt
REM ========================================================================

echo Building SWAP 4.2.0 for Windows...
echo.

REM Create build directories
if not exist "build" mkdir build
if not exist "build\x64" mkdir build\x64
if not exist "build\x64\Release" mkdir build\x64\Release
if not exist "bin" mkdir bin

REM Change to source directory
cd swap\source_swp_4.2.0

REM Set compiler flags based on original settings
set FFLAGS=/nologo /O2 /fpscomp:general /warn:declarations /warn:unused /warn:uncalled /warn:interfaces /Qsave /Qinit:zero /fpe:0 /fp:source /Qfp-speculation=off /module:"..\..\build\x64\Release\\" /object:"..\..\build\x64\Release\\" /libs:static /threads /c

echo Compiling SWAP source files...

REM Compile all Fortran source files in dependency order
ifort %FFLAGS% variables.f90
if errorlevel 1 goto :error

ifort %FFLAGS% swap_csv_output.f90
if errorlevel 1 goto :error

ifort %FFLAGS% sptabulated.f90
if errorlevel 1 goto :error

ifort %FFLAGS% WC_K_models_04_11.f90
if errorlevel 1 goto :error

ifort %FFLAGS% tillage.f90
if errorlevel 1 goto :error

ifort %FFLAGS% tridag.f90
if errorlevel 1 goto :error

ifort %FFLAGS% checkmassbal.f90
if errorlevel 1 goto :error

ifort %FFLAGS% fluxes.f90
if errorlevel 1 goto :error

ifort %FFLAGS% calcgwl.f90
if errorlevel 1 goto :error

ifort %FFLAGS% hysteresis.f90
if errorlevel 1 goto :error

ifort %FFLAGS% penmon.f90
if errorlevel 1 goto :error

ifort %FFLAGS% divdra.f90
if errorlevel 1 goto :error

ifort %FFLAGS% sharedsimulation.f90
if errorlevel 1 goto :error

ifort %FFLAGS% watstor.f90
if errorlevel 1 goto :error

ifort %FFLAGS% sharedexchange.f90
if errorlevel 1 goto :error

ifort %FFLAGS% convertdiscrvert.f90
if errorlevel 1 goto :error

ifort %FFLAGS% meteoday.f90
if errorlevel 1 goto :error

ifort %FFLAGS% oxygenstress.f90
if errorlevel 1 goto :error

ifort %FFLAGS% irrigation.f90
if errorlevel 1 goto :error

ifort %FFLAGS% rootextraction.f90
if errorlevel 1 goto :error

ifort %FFLAGS% frozencond.f90
if errorlevel 1 goto :error

ifort %FFLAGS% solute.f90
if errorlevel 1 goto :error

ifort %FFLAGS% integral.f90
if errorlevel 1 goto :error

ifort %FFLAGS% readswap.f90
if errorlevel 1 goto :error

ifort %FFLAGS% timecontrol.f90
if errorlevel 1 goto :error

ifort %FFLAGS% temperature.f90
if errorlevel 1 goto :error

ifort %FFLAGS% management_soil.f90
if errorlevel 1 goto :error

ifort %FFLAGS% soilwater.f90
if errorlevel 1 goto :error

ifort %FFLAGS% initialize.f90
if errorlevel 1 goto :error

ifort %FFLAGS% readmeteo.f90
if errorlevel 1 goto :error

ifort %FFLAGS% drainage.f90
if errorlevel 1 goto :error

ifort %FFLAGS% swapoutput.f90
if errorlevel 1 goto :error

ifort %FFLAGS% macrorate.f90
if errorlevel 1 goto :error

ifort %FFLAGS% calcgrid.f90
if errorlevel 1 goto :error

ifort %FFLAGS% snow.f90
if errorlevel 1 goto :error

ifort %FFLAGS% boundtop.f90
if errorlevel 1 goto :error

ifort %FFLAGS% swap.f90
if errorlevel 1 goto :error

ifort %FFLAGS% macroporeoutput.f90
if errorlevel 1 goto :error

ifort %FFLAGS% headcalc.f90
if errorlevel 1 goto :error

ifort %FFLAGS% boundbottom.f90
if errorlevel 1 goto :error

ifort %FFLAGS% cropgrowth.f90
if errorlevel 1 goto :error

ifort %FFLAGS% surfacewater.f90
if errorlevel 1 goto :error

ifort %FFLAGS% macropore.f90
if errorlevel 1 goto :error

ifort %FFLAGS% functions.f90
if errorlevel 1 goto :error

ifort %FFLAGS% meteodt.f90
if errorlevel 1 goto :error

ifort %FFLAGS% swap_main.f90
if errorlevel 1 goto :error

echo.
echo Linking executable...

REM Link all object files to create executable
ifort /OUT:"..\..\bin\swap.exe" /INCREMENTAL:NO /NOLOGO /SUBSYSTEM:CONSOLE ..\..\build\x64\Release\*.obj
if errorlevel 1 goto :error

cd ..\..

echo.
echo Build completed successfully!
echo Executable created: bin\swap.exe
echo.
goto :end

:error
echo.
echo ERROR: Build failed!
echo Please check that Intel Fortran Compiler is installed and in PATH
echo.
cd ..\..
pause
exit /b 1

:end
echo Build process finished.
pause
