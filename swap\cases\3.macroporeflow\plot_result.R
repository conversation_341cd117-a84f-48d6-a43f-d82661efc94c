# Wageningen Environmental Research
# <EMAIL>
#------------------------------------------------

# set start of program
TIMPRGSTART <- Sys.time()

# set modus
test <- FALSE

# read arguments
#------------------------------------------------

if (!test) {
   command_args <- commandArgs(trailingOnly = TRUE)
   if (length(command_args) != 0) {
     warning("ERROR: wrong usage of script")
     warning("Arguments: none")
     stop()
    }
  } else {
   command_args <- NULL
   setwd("d:/cases/3.macroporeflow")
  }
ctrl <- command_args[1]

# load libraries
#------------------------------------------------
source("../../Rsoftware/libraries.R")

# run R-script
#------------------------------------------------
message(str_c("\nProgram R-PROG started...\n"))

# ---- initial part of procedure ----

if(!interactive()) pdf(NULL)

file_gwl <- "./observed/gwl.csv"
file_dra <- "./observed/drainage.csv"
file_swp <- "./swap.swp"

# ---- main part of procedure ----

# load observed groundwater level
db_gwl <- read_csv(file = file_gwl, col_types = "Dd", lazy = FALSE, comment = "#") %>%
  select(date, value) %>%
  rename(observed = value)

# load observed drainage
db_dra <- read_csv(file = file_dra, col_types = "Dd", lazy = FALSE, comment = "#") %>%
  select(date, value) %>%
  group_by(date) %>%
  summarise(
    observed = sum(value),
    .groups = "drop"
  )

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output.csv")
db_swp <- read_csv_SWAP(file = file_csv, variable = c("GWL", "DRAINAGE")) %>%
  mutate(
    date = as_date(datetime)
  ) %>%
  select(date, GWL, DRAINAGE)

# ---- groundwater level ----

# combine data
db_fig <- db_swp %>%
  select(date, GWL) %>%
  rename(modelled = GWL)
db_fig <- left_join(x = db_fig, y = db_gwl, by = "date")

# set limits
ylim <- c(floor(min(c(db_fig$modelled, db_fig$observed), na.rm = TRUE) / 100) * 100, 0)
xpos <- min(db_fig$date)
ypos <- ylim[1] + 0.95 * (ylim[2] - ylim[1])

# set performance
db_prf <- get_modelperformance(actual = db_fig$observed / 100, predicted = db_fig$modelled / 100, performance = c("rmse", "rpearson")) %>%
  mutate(
    xpos = xpos, 
    ypos = ypos,
    label = str_c("RMSE: ", formatC(x = rmse, format = "f", digits = 2), "  Rpearson: ", formatC(x = rpearson, format = "f", digits = 2))
  )

# create plot
G1 <- ggplot(data = db_fig) +
  geom_line(aes(x = date, y = modelled / 100), colour = "blue") +
  geom_point(data = db_gwl, aes(x = date, y = observed / 100), colour = "red") +
  geom_text(data = db_prf, aes(x = xpos, y = ypos / 100, label = label), colour = "black", hjust = "inward", fontface = "italic") +
  coord_cartesian(ylim = ylim / 100) +
  labs(x = "", y = create_label(label = "Groundwater level", unit = "m + MSL")) +
  get_my_theme("figure")

# ---- drainage ----

# combine data
db_fig <- db_swp %>%
  select(date, DRAINAGE) %>%
  rename(modelled = DRAINAGE)
db_fig <- left_join(x = db_fig, y = db_dra, by = "date")

# set limits
ylim <- c(0, ceiling(max(c(db_fig$modelled, db_fig$observed), na.rm = TRUE) / 2) * 2)
xpos <- min(db_fig$date)
ypos <- ylim[1] + 0.95 * (ylim[2] - ylim[1])

# set performance
db_prf <- get_modelperformance(actual = db_fig$observed * 10, predicted = db_fig$modelled * 10, performance = c("rmse", "rpearson")) %>%
  mutate(
    xpos = xpos, 
    ypos = ypos,
    label = str_c("RMSE: ", formatC(x = rmse, format = "f", digits = 2), "  Rpearson: ", formatC(x = rpearson, format = "f", digits = 2))
  )

# create plot
G2 <- ggplot(data = db_fig) +
  geom_line(aes(x = date, y = modelled * 10), colour = "blue") +
  geom_point(data = db_dra, aes(x = date, y = observed * 10), colour = "red") +
  geom_text(data = db_prf, aes(x = xpos, y = ypos * 10, label = label), colour = "black", hjust = "inward", fontface = "italic") +
  coord_cartesian(ylim = ylim * 10) +
  labs(x = "", y = create_label(label = "Drainage", unit = "mm d-1")) +
  get_my_theme("figure")

# ---- combine plots ----

# combine
P1 <- ggplotGrob(G1)
P2 <- ggplotGrob(G2)
P <- rbind(P1, P2, size = "first")
P$widths <- unit.pmax(P1$widths, P2$widths)

ggsave(filename = str_c(OUTFIL, ".png"), plot = P, width = 10, height = 8, dpi = "retina", bg = "white")

# ---- return part of procedure ----

TIMPRGEND <- Sys.time()
TIMPRGCALC <- as.numeric(difftime(time1 = TIMPRGEND, time2 = TIMPRGSTART, units = "secs"))
message(paste0("\nProgram R-PROG successfully ended in ", floor(TIMPRGCALC / 60), " minutes and ", ceiling(TIMPRGCALC %% 60), " seconds"))
q(save = "no")
