# Wageningen Environmental Research
# <EMAIL>
#------------------------------------------------

# set start of program
TIMPRGSTART <- Sys.time()

# set modus
test <- FALSE

# read arguments
#------------------------------------------------

if (!test) {
   command_args <- commandArgs(trailingOnly = TRUE)
   if (length(command_args) != 0) {
     warning("ERROR: wrong usage of script")
     warning("Arguments: none")
     stop()
    }
  } else {
   command_args <- NULL
   setwd("d:/cases/2.grassgrowth")
  }
ctrl <- command_args[1]

# load libraries
#------------------------------------------------
source("../../Rsoftware/libraries.R")

# run R-script
#------------------------------------------------
message(str_c("\nProgram R-PROG started...\n"))

# ---- initial part of procedure ----

file_obs <- "./observed/yield.csv"
file_swp <- "./swap.swp"

# ---- main part of procedure ----

# load observed yield
db_obs <- read_csv(file = file_obs, col_types = "Dd", lazy = FALSE, comment = "#") %>%
  mutate(year = year(date)) %>%
  group_by(year) %>%
  summarise(
    date = date,
    value = cumsum(value),
    .groups = "drop"
  ) %>%
  select(date, value)

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output.csv")
db_swp <- read_csv_SWAP(file = file_csv, variable = c("PGRASSDM", "GRASSDM", "PMOWDM", "MOWDM")) %>%
  mutate(
    date = as_date(datetime),
    potential = PGRASSDM + PMOWDM,
    actual = GRASSDM + MOWDM
  ) %>%
  select(date, potential, actual)

# rearrange data
db_fig <- NULL
levels <- c("potential", "actual")
for (level in levels) {
  db_tmp <- db_swp %>%
    rename(value = all_of(level)) %>%
    mutate(level = all_of(level)) %>%
    select(date, level, value)
  db_fig <- rbind(db_fig, db_tmp)
}

# create factors
db_fig$level <- factor(x = db_fig$level, levels = levels, ordered = TRUE)

# create plot
G <- ggplot(data = db_fig) +
  geom_line(aes(x = date, y = value, colour = level)) +
  geom_point(data = db_obs, aes(x = date, y = value), colour = "red") +
  scale_colour_manual(name = create_label(label = "growth"), values = c("potential" = "green", "actual" = "black")) +
  labs(x = "", y = create_label(label = "Yield", unit = "kg ha-1")) +
  get_my_theme("figure")

ggsave(filename = str_c(OUTFIL, ".png"), width = 10, height = 5, dpi = "retina", bg = "white")

# ---- return part of procedure ----

TIMPRGEND <- Sys.time()
TIMPRGCALC <- as.numeric(difftime(time1 = TIMPRGEND, time2 = TIMPRGSTART, units = "secs"))
message(paste0("\nProgram R-PROG successfully ended in ", floor(TIMPRGCALC / 60), " minutes and ", ceiling(TIMPRGCALC %% 60), " seconds"))
q(save = "no")
