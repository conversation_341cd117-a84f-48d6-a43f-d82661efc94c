=== Compiler_Settings_4.2.0.txt ================================================================

==============================================================================
This file is part of:

Program     :  SWAP
Version     :  4.2.0
Releasedate :  08-June-2017
Platform    :  windows10; Linux (Ubuntu)

The Windows executable swap.exe was created using:

Compiler: 
   Intel� Parallel Studio XE 2019 Update 6 Composer Edition for Fortran Windows Integration 
   for Microsoft Visual Studio* 2017, Version 19.0.0052.15

Compiler Command Line settings:
   /nologo /O2 /I"D:\SYS\F77\Lib\TTUTIL4_27\TTutil427\x64\Release" 
   /fpscomp:general /warn:declarations /warn:unused /warn:uncalled 
   /warn:interfaces /Qsave /Qinit:zero /fpe:0 /fp:source /Qfp-speculation=off 
   /module:"x64\Release\\" /object:"x64\Release\\" /Fd"x64\Release\vc150.pdb"
   /libs:static /threads /c

Linker Command Line settings:
   /OUT:"x64\Release\SWAP_4.exe" /INCREMENTAL:NO /NOLOGO /MANIFEST
   /MANIFESTFILE:"x64\Release\SWAP_4.exe.intermediate.manifest"
   /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /SUBSYSTEM:CONSOLE 
   /IMPLIB:"D:\USR\SWAP\SWAP_V4\SWAP_current\x64\Release\SWAP_4.lib"

The object-files of Swap were linked with the library of ttutil version 4.27.
This ttutil-library was created using the settings:
   /nologo /O2 /fpscomp:general /module:"x64\Release\\" 
   /object:"x64\Release\\" /Fd"x64\Release\vc150.pdb" /libs:static /threads 
   /c/nologo /Od /module:"Release\\" /object:"Release\\" /libs:static /threads /c

The Linux executable was created with an Intel compiler.
Bash scripts are provided in separate file: compile_link_420.sh

==============================================================================

=== End of Compiler_Settings_4.2.0.txt  ========================================================
