# SWAP模型结构图

```mermaid
graph TD
    subgraph Legend [图例]
        direction LR
        A1(主控制器) --> B1(核心模块)
        A2(物理/过程模块) --> B2(管理模块)
        A3[基础设施/工具]
    end

    subgraph SWAP模型工厂
        %% 1. Power On & Control Center
        power_on("swap_main.f90 (电源总开关)") --> control_room("swap.f90 (总调度室/指挥中心)");

        %% 2. Infrastructure (Foundation)
        subgraph Infrastructure [基础设施: 图纸与字典]
            style Infrastructure fill:#f9f9f9,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
            vars("variables.f90 (总词典)")
            params("params.fi (常量表)")
            arrays("arrays.fi (尺寸表)")
        end

        %% 3. The Three Main Phases
        control_room -- iTask=1 --> phase1
        control_room -- iTask=2 --> phase2
        control_room -- iTask=3 --> phase3

        %% Phase 1: Initialization
        subgraph phase1 [Phase 1: 生产准备 (iTask=1)]
            direction TB
            p1_init("call Initialize") --> p1_read("call ReadSwap")
            p1_read --> p1_grid("call CalcGrid")
            p1_grid --> p1_modules("call ModuleName(1)...<br/>(SoilWater, CropGrowth, etc.)")
            p1_modules --> p1_output("call SwapOutput(1)")
        end

        %% Phase 2: Dynamic Simulation
        subgraph phase2 [Phase 2: 正式生产 (iTask=2) - 时间驱动循环]
            direction TB
            p2_start("timecontrol.f90 (启动循环)") --> p2_loop
            subgraph p2_loop [每日循环 (Do While t < tend)]
                direction TB
                %% Daily Six-Step
                meteo("readmeteo.f90 (接收原料)") --> penmon("penmon.f90 (计算需求)")
                penmon --> soilwater("soilwater.f90 (核心水处理车间)")
                soilwater --> root("rootextraction.f90 (根系车间)")
                root --> crop("cropgrowth.f90 (作物生长车间)")

                %% Management Modules
                subgraph p2_management [农业管理模块]
                    direction LR
                    irrigation("irrigation.f90")
                    drainage("drainage.f90")
                end
                crop --> p2_management

                %% Special Physics Modules
                subgraph p2_special [特种物理模块]
                    direction LR
                    snow("snow.f90")
                    frozen("frozencond.f90")
                    solute("solute.f90")
                end
                soilwater --> p2_special
            end
            p2_loop --> p2_end("timecontrol.f90 (更新时间, 调整步长)")
            p2_end --> p2_start
        end

        %% Phase 3: Finalization
        subgraph phase3 [Phase 3: 收尾与质检 (iTask=3)]
            direction TB
            p3_check("checkmassbal.f90 (总审计师)") --> p3_output("call SwapOutput(3)")
            p3_output --> p3_end("正常结束")
        end

        %% Connections for Infrastructure & Tools
        subgraph Tools [工具箱]
            style Tools fill:#f9f9f9,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
            functions("functions.f90")
            fluxes("fluxes.f90")
            calcgwl("calcgwl.f90")
        end

        Infrastructure --> control_room
        Infrastructure --> phase1
        Infrastructure --> phase2

        soilwater --> Tools
        crop --> Tools

    end

    %% Styling
    style power_on fill:#FFB6C1,stroke:#333,stroke-width:2px
    style control_room fill:#FFB6C1,stroke:#333,stroke-width:2px
    style soilwater fill:#ADD8E6,stroke:#333,stroke-width:2px
    style crop fill:#90EE90,stroke:#333,stroke-width:2px
    style meteo fill:#f2f2f2,stroke:#333
    style penmon fill:#f2f2f2,stroke:#333
    style root fill:#f2f2f2,stroke:#333
    style p2_management fill:#FFFACD,stroke:#333
    style irrigation fill:#FFFACD,stroke:#333
    style drainage fill:#FFFACD,stroke:#333
    style p2_special fill:#E6E6FA,stroke:#333
    style snow fill:#E6E6FA,stroke:#333
    style frozen fill:#E6E6FA,stroke:#333
    style solute fill:#E6E6FA,stroke:#333
```