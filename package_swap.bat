@echo off
REM ========================================================================
REM Package SWAP for Distribution
REM Creates a standalone package with executable and sample cases
REM ========================================================================

echo Packaging SWAP for distribution...
echo.

REM Check if executable exists
if not exist "bin\swap.exe" (
    echo ERROR: swap.exe not found in bin directory
    echo Please run build_swap_gfortran.bat first to compile the model
    pause
    exit /b 1
)

REM Create package directory
set PACKAGE_DIR=SWAP_Package_%DATE:~0,4%%DATE:~5,2%%DATE:~8,2%
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

echo Creating package directory: %PACKAGE_DIR%
echo.

REM Copy executable
echo Copying executable...
mkdir "%PACKAGE_DIR%\bin"
copy "bin\swap.exe" "%PACKAGE_DIR%\bin\"

REM Copy documentation
echo Copying documentation...
mkdir "%PACKAGE_DIR%\docs"
copy "docs\*.md" "%PACKAGE_DIR%\docs\" 2>nul
copy "docs\*.txt" "%PACKAGE_DIR%\docs\" 2>nul
copy "swap\doc\*.pdf" "%PACKAGE_DIR%\docs\" 2>nul

REM Copy license
echo Copying license...
copy "swap\license\*.txt" "%PACKAGE_DIR%\" 2>nul

REM Copy sample cases
echo Copying sample cases...
mkdir "%PACKAGE_DIR%\cases"
xcopy "swap\cases\*" "%PACKAGE_DIR%\cases\" /s /e /q

REM Copy crop and meteorological data
echo Copying reference data...
mkdir "%PACKAGE_DIR%\xdata"
xcopy "swap\xdata\*" "%PACKAGE_DIR%\xdata\" /s /e /q 2>nul

REM Create run scripts for the package
echo Creating run scripts...

REM Create main run script
echo @echo off > "%PACKAGE_DIR%\run_hupselbrook.bat"
echo echo Running SWAP 1-hupselbrook case study... >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo echo. >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo cd cases\1.hupselbrook >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo ..\..\bin\swap.exe swap >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo echo. >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo echo Execution completed. Check result files in this directory. >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo pause >> "%PACKAGE_DIR%\run_hupselbrook.bat"
echo cd ..\.. >> "%PACKAGE_DIR%\run_hupselbrook.bat"

REM Create README for the package
echo Creating README...
echo SWAP Model Package > "%PACKAGE_DIR%\README.txt"
echo ================== >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo This package contains the SWAP hydrological model executable and sample cases. >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo Quick Start: >> "%PACKAGE_DIR%\README.txt"
echo 1. Double-click run_hupselbrook.bat to run the sample case >> "%PACKAGE_DIR%\README.txt"
echo 2. Results will be generated in cases\1.hupselbrook\ directory >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo Directory Structure: >> "%PACKAGE_DIR%\README.txt"
echo - bin\          : SWAP executable >> "%PACKAGE_DIR%\README.txt"
echo - cases\        : Sample input cases >> "%PACKAGE_DIR%\README.txt"
echo - docs\         : Documentation >> "%PACKAGE_DIR%\README.txt"
echo - xdata\        : Reference data >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo For more information, see the documentation in the docs\ folder. >> "%PACKAGE_DIR%\README.txt"

REM Create ZIP package if 7zip is available
where 7z >nul 2>&1
if not errorlevel 1 (
    echo Creating ZIP archive...
    7z a -tzip "%PACKAGE_DIR%.zip" "%PACKAGE_DIR%\*"
    echo ZIP package created: %PACKAGE_DIR%.zip
) else (
    echo 7zip not found. Package created as folder: %PACKAGE_DIR%
    echo You can manually zip this folder for distribution.
)

echo.
echo ========================================================================
echo Packaging completed successfully!
echo Package location: %PACKAGE_DIR%
echo ========================================================================
echo.
echo The package contains:
echo - SWAP executable (bin\swap.exe)
echo - Sample cases including 1-hupselbrook
echo - Documentation and reference data
echo - Ready-to-run scripts
echo.
pause
