**********************************************************************************
* Filename: swap.dra                  
* Contents: SWAP 4 - Input data for extended drainage
**********************************************************************************
* Comment area:
* Testbank of SWAP: build with template of dra-file
*
**********************************************************************************

*** EXTENDED DRAINAGE SECTION ***

**********************************************************************************
* Part 0: General

  SWDIVD = 1                 ! Calculate vertical distribution of drainage flux in groundwater [Y=1, N=0]

* If SWDIVD = 1, specify anisotropy factor COFANI (horizontal/vertical saturated hydraulic
* conductivity) for each soil layer (maximum MAHO), [0.0001..1000 -, R]:
  COFANI = 10.0 10.0 10.0

* Switch to adjust upper boundary of model discharge layer
  SWDISLAY = 0               ! 0 = No adjustment
                             ! 1 = Adjusment based on depth of top of model discharge
                             ! 2 = Adjusment based on factor of top of model discharge

**********************************************************************************

**********************************************************************************
* Part 0: Reference level

  ALTCU = 0.0                ! Altitude of the control unit relative to reference level [-300000..300000 cm, R]

**********************************************************************************

**********************************************************************************
* Part 1: drainage characteristics 

  NRSRF = 2                  ! number of subsurface drainage levels [1..5, I]

* Table with physical characteristics of each subsurface drainage level:
* Variables RENTRY, REXIT, WIDTHR and TALUDR must have realistic values in case of open channels
* LEVEL = Drainage level number [1..NRSRF, I]
* SWDTYP = Type of drainage medium [open=0, closed=1] 
* L = Spacing between channels/drains [1..100000 m, R]
* ZBOTDRE = Altitude of bottom of channel or drain [ALTCU-1000..ALTCU-0.01 cm,R]
* GWLINF = Groundwater level for maximum infiltration [-1000..0 cm, R]
* RDRAIN = Drainage resistance [1..100000 d, R]
* RINFI = Infiltration resistance  [1..100000 d, R]
* RENTRY = Entry resistance  [0..100 d, R]
* REXIT = Exit resistance   [0..100 d, R]
* WIDTHR = Bottom width of channel [0..10000 cm, R]
* TALUDR = Side-slope (dh/dw) of channel [0.01..5, R]

 LEV  SWDTYP      L  ZBOTDRE   GWLINF  RDRAIN   RINFI  RENTRY  REXIT  WIDTHR  TALUDR
   1       0  390.0   -115.0  -1000.0   461.0  1000.0     1.0    5.0   100.0    0.66
   2       0  170.0    -60.0  -1000.0   296.0   500.0     1.0    5.0    50.0    1.00
* End of table

* Switch to introduce rapid subsurface drainage [0..2, I]
  SWNRSRF = 0                ! 0 = No rapid drainage
                             ! 1 = Rapid drainage in the highest drainage system (implies adjustment of RDRAIN of highest drainage system)
                             ! 2 = Rapid drainage as interflow according to a power relation (implies adjustment of RDRAIN of highest drainage system)


* Switch to adjust the bottom of the model discharge layer in case of lateral (SWDIVD=1) interflow or rapid drainage (SWNRSRF=1 or SWNRSRF=2). 
* In case of SWTOPNRSRF = 1, the bottom of the highest order drainage system (ZBORDR(NUMDRAIN)) represents the maximum depth of the interflow.  
 SWTOPNRSRF = 0              ! Switch to enable adjustment of model discharge layer [0,1, I] 

**********************************************************************************

**********************************************************************************
* Part 2: Specification and control of surface water system

* Switch for interaction with surface water system [1..3, I] 
  SWSRF = 2                  ! 1 = No interaction with surface water system
                             ! 2 = Surface water system is simulated without separate primary system 
                             ! 3 = Surface water system is simulated with separate primary system

* If SWSRF = 2, specify option for surface water level of secondary system [1..2, I]
  SWSEC = 2                  ! 1 = Surface water level is input
                             ! 2 = Surface water level is simulated


* Miscellaneous parameters
  WLACT = -77.0              ! Initial surface water level [ALTCU-1000..ALTCU cm,R]
  OSSWLM = 2.5               ! Criterium for warning about oscillation [0..10 cm, R]

* Management of surface water levels
  NMPER = 28                 ! Number of management periods [1..3660, I]

* For each management period specify:
* IMPER = Index of management period [1..NMPER, I]
* IMPEND = Date that period ends [YYYY-MM-DD]
* SWMAN = Type of water management 1 = fixed weir crest, 2 = automatic weir [1..2, I]
* WSCAP = Surface water supply capacity [0..100 cm/d, R]
* WLDIP = Allowed dip of surface water level before starting supply [0..100 cm, R]
* INTWL = Length of water-level adjustment period (SWMAN = 2 only) [1..31 d, I]

 IMPER_4B      IMPEND  SWMAN  WSCAP  WLDIP  INTWL
        1  1997-01-01      1    0.0    0.0      1
        2  1997-01-15      1    0.0    0.0      1
        3  1997-02-27      1    0.0    0.0      1
        4  1997-03-19      1    0.0    0.0      1
        5  1997-06-03      1    0.0    0.0      1
        6  1997-06-18      1    0.0    0.0      1
        7  1997-07-03      1    0.0    0.0      1
        8  1997-08-21      1    0.0    0.0      1
        9  1997-10-07      1    0.0    0.0      1
       10  1997-10-30      1    0.0    0.0      1
       11  1998-02-05      1    0.0    0.0      1
       12  1998-02-11      1    0.0    0.0      1
       13  1998-02-26      1    0.0    0.0      1
       14  1998-03-12      1    0.0    0.0      1
       15  1998-03-19      1    0.0    0.0      1
       16  1998-03-30      1    0.0    0.0      1
       17  1998-04-10      1    0.0    0.0      1
       18  1998-04-18      1    0.0    0.0      1
       19  1998-05-04      1    0.0    0.0      1
       20  1998-05-27      1    0.0    0.0      1
       21  1998-08-14      1    0.0    0.0      1
       22  1998-09-15      1    0.0    0.0      1
       23  1998-12-16      1    0.0    0.0      1
       24  1999-01-18      1    0.0    0.0      1
       25  1999-01-26      1    0.0    0.0      1
       26  1999-06-24      1    0.0    0.0      1
       27  1999-12-10      1    0.0    0.0      1
       28  1999-12-31      1    0.0    0.0      1
* End of table

* Switch for type of discharge relationship [1..2, I]
  SWQHR = 1                  ! 1 = Exponential relationship
                             ! 2 = Table

* If SWQHR = 1, specify:
  SOFCU = 100.0              ! Size of the control unit [0.1..100000.0 ha, R]

* If SWQHR = 1, specify exponential discharge relation for all periods: 
* IMPER = Index of management period [1..NMPER, I]
* HBWEIR = Weir crest; levels above soil surface are allowed, but simulated surface water levels should remain below 100 cm above soil surface; 
*   the crest must be higher than the deepest channel bottom of the secondary system (ZBOTDR(1 or 2), [ALTCU-ZBOTDR..ALTCU+100 cm,R].
*   If SWMAN = 2, HBWEIR represents the lowest possible weir position.
* ALPHAW = Alpha-coefficient of discharge formula [0.1..50.0, R]
* BETAW = Beta-coefficient of discharge formula [0.5..3.0, R]

 IMPER_4C  HBWEIR  ALPHAW  BETAW
        1   -96.0     1.7    1.5
        2   -45.0     1.7    1.5
        3   -96.0     1.7    1.5
        4   -46.5     1.7    1.5
        5   -96.0     1.7    1.5
        6   -56.2     1.7    1.5
        7   -96.0     1.7    1.5
        8   -67.0     1.7    1.5
        9   -46.5     1.7    1.5
       10   -68.0     1.7    1.5
       11   -46.5     1.7    1.5
       12   -38.8     1.7    1.5
       13   -34.0     1.7    1.5
       14   -58.0     1.7    1.5
       15   -68.0     1.7    1.5
       16   -51.0     1.7    1.5
       17   -66.0     1.7    1.5
       18   -63.0     1.7    1.5
       19   -73.0     1.7    1.5
       20   -51.0     1.7    1.5
       21   -61.0     1.7    1.5
       22   -63.3     1.7    1.5
       23   -71.0     1.7    1.5
       24   -46.0     1.7    1.5
       25   -46.0     1.7    1.5
       26   -66.0     1.7    1.5
       27   -61.0     1.7    1.5
       28   -46.6     1.7    1.5
* End of table

**********************************************************************************

* End of .dra file           !
