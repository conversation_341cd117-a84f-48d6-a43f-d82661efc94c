**********************************************************************************
* Filename: swap.swp
* Contents: SWAP 4 - Main input data
**********************************************************************************
* Comment area:
* Testbank of SWAP: build with template of swp-file
*
**********************************************************************************

*   The main input file .swp contains the following sections:
*           - General section
*           - Meteorology section
*           - Crop section
*           - Soil water section
*           - Lateral drainage section
*           - Bottom boundary section
*           - Heat flow section
*           - Solute transport section

**********************************************************************************

*** GENERAL SECTION ***

**********************************************************************************
* Part 1: Environment

  PROJECT = 'andelst'        ! Project description [A80]
  PATHWORK = '.\'            ! Path to work folder [A80]
  PATHATM = '.\'             ! Path to folder with weather files [A80]
  PATHCROP = '.\'            ! Path to folder with crop files [A80]
  PATHDRAIN = '.\'           ! Path to folder with drainage files [A80]
  
* Switch, display progression of simulation run to screen:  
  SWSCRE = 0                 ! 0 = no display to screen
                             ! 1 = display water balance components
                             ! 2 = display daynumber

* Switch for printing errors to screen:
  SWERROR = 0                ! 0 = no display to screen
                             ! 1 = display error to screen

**********************************************************************************

**********************************************************************************
* Part 2: Simulation period

  TSTART = 1998-01-01        ! Start date of simulation run [YYYY-MM-DD]
  TEND = 1999-04-26            ! End date of simulation run [YYYY-MM-DD]

**********************************************************************************

**********************************************************************************
* Part 3: Output dates 

* Number of output times during a day
  NPRINTDAY = 1              ! Number of output times during a day [1..1440, I]

* Specify dates for output of state variables and fluxes
  SWMONTH = 0                ! Switch, output each month [Y=1, N=0]

* If SWMONTH = 0, choose output interval and/or specific dates
  PERIOD = 1                 ! Fixed output interval, ignore = 0 [0..366, I]
  SWRES = 0                  ! Switch, reset output interval counter each year [Y=1, N=0]
  SWODAT = 0                 ! Switch, extra output dates are given in table below [Y=1, N=0]

* Output times for overall water and solute balances in *.BAL and *.BLC file: choose output
* at a fixed date each year or at different dates:
  SWYRVAR = 0                ! 0 = each year output at the same date
                             ! 1 = output at different dates

* If SWYRVAR = 0 specify fixed date:
  DATEFIX = 31 12            ! Specify day and month for output of yearly balances [dd mm]

**********************************************************************************

**********************************************************************************
* Part 4: Output files

* General information
  OUTFIL = 'result'          ! Generic file name of output files, [A16]
  SWHEADER = 0               ! Print header at the start of each balance period [Y=1, N=0]

* Optional files
  SWWBA = 1                  ! Switch, output cumulative water balance [Y=1, N=0]
  SWEND = 0                  ! Switch, output end-conditions [Y=1, N=0]
  SWVAP = 0                  ! Switch, output soil profiles of moisture, solute and temperature [Y=1, N=0]
  SWBAL = 0                  ! Switch, output file with yearly water balance [Y=1, N=0]
  SWBLC = 0                  ! Switch, output file with detailed yearly water balance [Y=1, N=0]
  SWSBA = 0                  ! Switch, output file of cumulative solute balance [Y=1, N=0]
  SWATE = 0                  ! Switch, output file with soil temperature profiles [Y=1, N=0]
  SWBMA = 0                  ! Switch, output file with water fluxes, only for macropore flow [Y=1, N=0]
  SWDRF = 0                  ! Switch, output of drainage fluxes, only for extended drainage [Y=1, N=0]
  SWSWB = 0                  ! Switch, output surface water reservoir, only for extended drainage [Y=1, N=0]
  SWINI = 0                  ! Switch, output of initial SoilPhysParam and HeatParam [Y=1, N=0]  
  SWINC = 0                  ! Switch, output of water balance increments [Y=1, N=0]
  SWCRP = 0                  ! Switch, output of simple or detailed crop growth model [Y=1, N=0]
  SWSTR = 0                  ! Switch, output of stress values for wetness, drought, salinity and frost [Y=1, N=0]
  SWIRG = 0                  ! Switch, output of irrigation gifts [Y=1, N=0]

* Specific CSV output file? (default: no)
  SWCSV = 1                  ! Switch, output of variables to be specified [Y=1, N=0]

  INLIST_CSV = 'gwl,drainage'

* Specific CSV output file? (default: no)
  SWCSV_TZ = 0               ! Switch, output of variables to be specified [Y=1, N=0]

* Optional output files for water quality models or other specific use

* Switch, output file with formatted hydrological data:
  SWAFO = 0                  ! 0 = no output
                             ! 1 = output to a file named *.AFO
                             ! 2 = output to a file named *.BFO

* Switch, output file with unformatted hydrological data:
  SWAUN = 0                  ! 0 = no output
                             ! 1 = output to a file named *.AUN
                             ! 2 = output to a file named *.BUN

**********************************************************************************

*** METEOROLOGY SECTION ***

**********************************************************************************
* General data

* File name
  METFIL = 'andelst_meteo'        ! File name of meteorological data, in case of yearly files without extension .YYY, [A200]
                             ! Extension is equal to last 3 digits of year, e.g. 022 denotes year 2022
                             ! In case of meteorological in one file use extension .met

* Details of meteo station:
  LAT = 52.0                 ! Latitude of meteo station [-90..90 degrees, R, North = +]
  
* Type of weather data for potential evapotranspiration
  SWETR = 1                  ! 0 = Use basic weather data and apply Penman-Monteith equation
                             ! 1 = Use reference evapotranspiration data in combination with crop factors

* In case of daily meteorological weather records (only if SWETR = 1):
  SWETSINE = 0               ! Switch, distribute daily Tp and Ep according to sinus wave [Y=1, N=0]

* Switch for use of actual rainfall intensity (only if SWETR = 1):
  SWRAIN = 3                 ! 0 = Use daily rainfall amounts
                             ! 1 = Use daily rainfall amounts + mean intensity
                             ! 2 = Use daily rainfall amounts + duration
                             ! 3 = Use detailed rainfall records (dt < 1 day), as supplied in separate file

* If SWRAIN = 3, then specify file name of file with detailed rainfall data
  RAINFIL = 'andelst_rain'      ! File name of detailed rainfall data without extension .YYY, [A200]
                             ! Extension is equal to last 3 digits of year, e.g. 003 denotes year 2003

**********************************************************************************

*** CROP SECTION ***

**********************************************************************************
* Part 1: Crop rotation scheme

* Switch for bare soil or cultivated soil:  
  SWCROP = 1                 ! 0 = Bare soil
                             ! 1 = Cultivated soil

* Specify for each crop (maximum MACROP):
* CROPSTART = date of crop emergence [YYYY-MM-DD]
* CROPEND = date of crop harvest [YYYY-MM-DD]
* CROPFIL = name of file with crop input parameters without extension .CRP, [A40]
* CROPTYPE = growth module: 1 = simple; 2 = detailed, WOFOST general; 3 = detailed, WOFOST grass

  CROPSTART     CROPEND     CROPFIL  CROPTYPE
 1998-01-01  1998-08-20  'wintcer1'         1
 1999-01-01  1999-04-26  'wintcer2'         1
* End of table

  RDS = 320.0                ! Maximum rooting depth allowed by the soil profile, [1..5000 cm, R]

**********************************************************************************

**********************************************************************************
* Part 2: Fixed irrigation applications

* Switch for fixed irrigation applications
  SWIRFIX = 0                ! 0 = no irrigation applications are prescribed
                             ! 1 = irrigation applications are prescribed


**********************************************************************************

*** SOIL WATER SECTION ***

**********************************************************************************
* Part 1: Initial soil moisture condition

* Switch, type of initial soil moisture condition:
  SWINCO = 2                 ! 1 = pressure head as function of soil depth
                             ! 2 = pressure head of each compartment is in hydrostatic equilibrium with initial groundwater level
                             ! 3 = read final pressure heads from output file of previous Swap simulation

* If SWINCO = 2, specify initial groundwater level:
  GWLI   = -80.9             ! Initial groundwater level, [-10000..100 cm, R]

**********************************************************************************

**********************************************************************************
* Part 2: Ponding, runoff and runon

* Ponding
* Switch for variation ponding threshold for runoff
  SWPONDMX = 0               ! 0 = Ponding threshold for runoff is constant
                             ! 1 = Ponding threshold for runoff varies in time

* If SWPONDMX = 0, specify
  PONDMX = 1.0               ! In case of ponding, minimum thickness for runoff [0..1000 cm, R]

* Runoff
  RSRO = 0.001               ! Drainage resistance for surface runoff [0.001..1.0 d, R]
  RSROEXP = 1.0              ! Exponent in drainage equation of surface runoff [0.01..10.0 -, R]

* Runon
  SWRUNON = 0                ! Switch, use of runon data [Y=1, N=0]

**********************************************************************************

**********************************************************************************
* Part 3: Soil evaporation

  CFEVAPPOND = 1.25          ! When ETref is used, evaporation coefficient in case of ponding  [0..3 -, R]

* Switch for use of soil factor CFBS to calculate Epot from ETref:
  SWCFBS = 1                 ! 0 = soil factor is not used
                             ! 1 = soil factor is used

* If SWCFBS = 1, specify coefficient CFBS:
  CFBS = 1.2                 ! Coefficient for potential soil evaporation, [0.5..1.5 -, R]

* Switch, method for reduction of potential soil evaporation:
  SWREDU = 2                 ! 0 = reduction to maximum Darcy flux
                             ! 1 = reduction to maximum Darcy flux and to maximum Black (1969)
                             ! 2 = reduction to maximum Darcy flux and to maximum Boesten/Stroosnijder (1986)

* If SWREDU = 2, specify:
 COFREDBO = 0.79             ! Soil evaporation coefficient of Boesten/Stroosnijder [0..1 cm1/2, R]

**********************************************************************************

**********************************************************************************
* Part 4: Vertical discretization of soil profile

* Specify the following data (maximum MACP lines):
* ISUBLAY  = number of sub layer, start with 1 at soil surface [1..MACP, I]
* ISOILLAY = number of soil physical layer, start with 1 at soil surface [1..MAHO, I]
* HSUBLAY  = height of sub layer [0..1.d4 cm, R]
* HCOMP    = height of compartments in the sub layer [0.0..1000.0 cm, R]
* NCOMP    = number of compartments in the sub layer (Mind NCOMP = HSUBLAY/HCOMP) [1..MACP, I]

 ISUBLAY  ISOILLAY  HSUBLAY  HCOMP  NCOMP
       1         1     12.0    1.0     12
       2         2     14.0    1.0     14
       3         3      8.0    1.0      8
       4         4     16.0    2.0      8
       5         5     20.0    2.0     10
       6         6     50.0    2.5     20
       7         7    100.0    5.0     20
       8         8    100.0    5.0     20
* End of table

**********************************************************************************

**********************************************************************************
* Part 5: Soil hydraulic functions

* Switch for analytical functions or tabular input:
  SWSOPHY = 0                ! 0 = Analytical functions with input of Mualem - van Genuchten parameters
                             ! 1 = Soil physical tables

* If SWSOPHY = 0, specify MvG parameters for each soil physical layer:
* ORES = Residual water content [0..1 cm3/cm3, R]
* OSAT = Saturated water content [0..1 cm3/cm3, R]
* ALFA = Parameter alfa of main drying curve [0.0001..100 /cm, R]
* NPAR = Parameter n [1.001..9 -, R]
* KSATFIT = Fitting parameter Ksat of hydraulic conductivity function [1.d-5..1d5 cm/d, R]
* LEXP = Exponent in hydraulic conductivity function [-25..25 -, R]
* H_ENPR = Air entry pressure head [-40.0..0.0 cm, R]
* KSATEXM = Measured hydraulic conductivity at saturated conditions [1.d-5..1d5 cm/d, R]
* BDENS = Dry soil bulk density [100..1d4 mg/cm3, R]

  ORES   OSAT    ALFA    NPAR  KSATFIT     LEXP  H_ENPR  KSATEXM   BDENS
 0.055  0.405  0.0289  1.0930     1.00  -10.502   -10.0     1.00  1300.0
 0.055  0.405  0.0289  1.0930     3.08  -10.502   -10.0     3.08  1300.0
 0.100  0.393  0.0075  1.1080     0.17  -14.455   -10.0     0.17  1300.0
 0.010  0.395  0.0172  1.0925     1.63   -5.819   -10.0     1.63  1300.0
 0.000  0.444  0.0117  1.0735     2.51   -0.254   -10.0     2.51  1300.0
 0.005  0.442  0.0078  1.0870     1.25   -7.713   -10.0     1.25  1300.0
 0.010  0.525  0.0050  1.0800     1.37   -7.465   -10.0     1.37  1300.0
 0.010  0.525  0.0050  1.0800    10.00   -7.465   -10.0    10.00  1300.0
* End of table

**********************************************************************************

**********************************************************************************
* Part 6: Hysteresis of soil water retention function

* Switch for hysteresis:
  SWHYST = 0                 ! 0 = no hysteresis
                             ! 1 = hysteresis, initial condition wetting
                             ! 2 = hysteresis, initial condition drying

**********************************************************************************

**********************************************************************************
* Part 7: Preferential flow due to macropores

* Switch for macropore flow [0..2, I]:
  SWMACRO = 1                ! 0 = no macropore flow
                             ! 1 = macropore flow

* If SWMACRO = 1, specify parameters for extended macropore flow:
  Z_AH = -26.0               ! Depth bottom A-horizon [-1000..0 cm, R]
  Z_IC = -100.0              ! Depth bottom Internal Catchment (IC) domain [-1000..0 cm, R]
  Z_ST = -220.0              ! Depth bottom Static macropores [-1000..0 cm, R]
  VLMPSTSS = 0.04            ! Volume of Static Macropores at Soil Surface [0..0.5 cm3/cm3, R]
  PPICSS = 0.5               ! Proportion of IC domain at Soil Surface [0..0.99 -, R]
  NUMSBDM = 4                ! Number of Subdomains in IC domain [0..MaDm-2 -, I]
  POWM = 1.                  ! Power M for frequency distribut. curve IC domain (OPTIONAL, default 1.0) [0..100 -, R]
  RZAH = 0.0                 ! Fraction macropores ended at bottom A-horizon [OPTIONAL, default 0.0] [0..1 -, R]
  SPOINT = 1.                ! Symmetry Point for freq. distr. curve [OPTIONAL, default 1.0] [0..1 -, R]
  SWPOWM = 0                 ! Switch for double convex/concave freq. distr. curve (OPTIONAL, Y=1, N=0; default: 0) [0..1 -, I]
  DIPOMI = 10.0              ! Minimal diameter soil polygones (shallow) [0.1..1000 cm, R]
  DIPOMA = 50.0              ! Maximal diameter soil polygones (deep) [0.1..1000 cm, R]
  
* Tabel with shrinkage characteristics
* SWSOILSHR = Switch for kind of soil for determining shrinkage curve: 0 = rigid soil, 1 = clay, 2 peat [0..2 -, I]
* SWSHRINP  = Switch for determining shrinkage curve [1..2 -, I]:  1 = parameters for curve are given;
*                                                                  2 = typical points of curve are given 
* GEOMFAC =  Geometry factor (3 = isotropic shrinkage) [0..100, R]
* SHRPARA to SHRPARE = parameters for describing shrinkage curves, 
* depending on combination of SWSOILSHR and SWSHRINP [-1000..1000, R]:
* SWSOILSHR = 0                : 0 variables required (all dummies)
* SWSOILSHR = 1,  SWSHRINP 1 = : 3 variables required (SHRPARA to SHRPARC)
* SWSOILSHR = 1,  SWSHRINP 2 = : 2 variables required (SHRPARA to SHRPARB)
* SWSOILSHR = 2,  SWSHRINP 1 = : 4 variables required (SHRPARA to SHRPARD)
* SWSOILSHR = 2,  SWSHRINP 2 = : 5 variables required (SHRPARA to SHRPARE)

  SWSOILSHR  SWSHRINP  THETCRMP  GEOMFAC  SHRPARA  SHRPARB  SHRPARC  SHRPARD  SHRPARE
          1         2     0.385      3.0    0.343   0.5390      0.0      0.0      0.0
          1         2     0.385      3.0    0.343   0.5392      0.0      0.0      0.0
          1         2     0.354      3.0    0.415   0.6350      0.0      0.0      0.0
          1         2     0.375      3.0    0.400   0.6400      0.0      0.0      0.0 
          1         2     0.422      3.0    0.412   0.7840      0.0      0.0      0.0 
          1         2     0.420      3.0    0.406   0.6883      0.0      0.0      0.0 
          1         2     0.499      3.0    0.495   0.9000      0.0      0.0      0.0 
          1         2     0.499      3.0    0.495   0.9000      0.0      0.0      0.0 
* End of table

  ZNCRAR = -5.0               ! Depth at which crack area of soil surface is calculated [-100..0 cm, R]

* Start of Tabel with sorptivity characteristics
* SWSORP = Switch for kind of sorptivity function [1..2 -, I]:  
*               1 = calculated from hydraulic functions according to Parlange
*               2 = emperical function from measurements
* SORPFACPARL = factor for modifying Parlange function (OPTIONAL, default 1.0) [0..100 -, R]
* SORPMAX     = maximal sorptivity at theta residual [0..100 cm/d**0.5, R]
* SORPALFA    = fitting parameter for emperical sorptivity curve [-10..10 -, R]

  SWSORP  SORPFACPARL  SORPMAX  SORPALFA
       1         0.33      0.3       0.5
       1         0.33      0.3       0.5
       1         0.33      0.6       0.5
       1         0.50      5.0       0.5
       1         0.50      5.0       0.5
       1         0.50      5.0       0.5
       1         0.50      5.0       0.5
       1         0.50      5.0       0.5
* End of table

* Shape factor Darcy exchange
  SHAPEFACMP = 1.5
  CRITUNDSATVOL = 0.1
  SWDARCY = 1

  SWDRRAP = 1                 ! Switch for rapid drainage  [0..1 -, I]:  
  RAPDRARESREF = 50.0         ! Reference rapid drainage resistance [0..1.E+10 /d, R] 
  RAPDRAREAEXP = 1.0          ! Exponent for reaction rapid drainage to dynamic crack width [0..100 -, R]

* Number of drainage system connected to rapid drainage 
  NUMLEVRAPDRA = 1            !  [1..NRLEVS, -, I] 
 
* Threshold value for ponding (cm) on soil surface before overland flow into macropores starts    
  PNDMXMP = 0.0               !  [0.0 .. 10.0,  cm, R]
                             
**********************************************************************************

**********************************************************************************
* Part 8: Snow and frost

* Switch, calculate snow accumulation and melt:
  SWSNOW = 0                 ! 0 = no simulation of snow
                             ! 1 = simulation of snow accumulation and melt

* Switch, in case of frost reduce soil water flow:
  SWFROST = 0                ! 0 = no simulation of frost
                             ! 1 = simulation of reduction soil water flow due to frost

**********************************************************************************

**********************************************************************************
* Part 9: Numerical solution of Richards' equation for soil water flow

  DTMIN = 0.00001            ! Minimum timestep [1.d-7..0.1 d, R]
  DTMAX = 0.04               ! Maximum timestep [dtmin..1 d, R]
  GWLCONV = 999.0            ! Maximum difference of groundwater level between time steps [1.d-5..1000 cm, R]
  CRITDEVH1CP = 0.01         ! Maximum relative difference in pressure heads per compartment [1.0d-10..1.d3 -, R]
  CRITDEVH2CP = 0.1          ! Maximum absolute difference in pressure heads per compartment [1.0d-10..1.d3 cm, R]
  CRITDEVPONDDT = 0.0001     ! Maximum water balance error of ponding layer [1.0d-6..0.1 cm, R]
  MAXIT = 30                 ! Maximum number of iteration cycles [5..100 -, I]
  MAXBACKTR = 3              ! Maximum number of back track cycles within an iteration cycle [1..10 -,I]

* Switch for averaging method of hydraulic conductivity [1..4 -, I]:
  SWKMEAN = 1                ! 1 = unweighted arithmic mean
                             ! 2 = weighted arithmic mean
                             ! 3 = unweighted geometric mean
                             ! 4 = weighted geometric mean
                             ! 5 = unweighted harmonic mean
                             ! 6 = weighted harmonic mean

* Switch for updating hydraulic conductivity during iteration [0..1 -, I]:
  SWKIMPL = 0                ! 0 = no update
                             ! 1 = update

**********************************************************************************

*** LATERAL DRAINAGE SECTION ***

**********************************************************************************
* Specify whether lateral drainage to surface water should be included

* Switch, simulation of lateral drainage:
  SWDRA = 1                  ! 0 = no simulation of drainage
                             ! 1 = simulation with basic drainage routine
                             ! 2 = simulation of drainage with surface water management

* If SWDRA = 1 specify name of file with drainage input data:
  DRFIL = 'swap'             ! File name with drainage input data without extension .DRA [A16]

**********************************************************************************

*** BOTTOM BOUNDARY SECTION ***

**********************************************************************************
* Bottom boundary condition

* Switch for file with bottom boundary data:
  SWBBCFILE = 1              ! 0 = data are specified in current file
                             ! 1 = data are specified in a separate file

**********************************************************************************
* If SWBBCFILE = 1 specify name of file with bottom boundary data:
  BBCFIL = 'swap'            ! File name without extension .BBC [A32]

**********************************************************************************

*** HEAT FLOW SECTION ***

**********************************************************************************
* Switch for simulation of heat transport:
  SWHEA = 1                  ! 0 = no simulation of heat transport
                             ! 1 = simulation of heat transport

* Switch for calculation method:
  SWCALT = 2                 ! 1 = analytical method
                             ! 2 = numerical method

* In case of the numerical method (SWCALT = 2) specify:
* Specify for each physical soil layer the soil texture (g/g mineral parts) and the organic matter content (g/g dry soil):

 PSAND  PSILT  PCLAY  ORGMAT
 0.194  0.529  0.277   0.021
 0.194  0.529  0.277   0.021
 0.191  0.521  0.289   0.016
 0.188  0.512  0.300   0.011
 0.143  0.509  0.348   0.010
 0.147  0.480  0.373   0.010
 0.157  0.471  0.372   0.010
 0.157  0.471  0.372   0.010
* End of table

* If SWINCO = 1 or 2, list initial temperature TSOIL [-50..50 degree C, R] as function of soil depth ZH [-100000..0 cm, R]:

     ZH  TSOIL
   -0.0   10.0
 -320.0   10.0
* End of table

* Define top boundary condition: 
  SWTOPBHEA = 1              ! 1 = use air temperature of meteo input file as top boundary
                             ! 2 = use measured top soil temperature as top boundary

* Define bottom boundary condition:
  SWBOTBHEA = 1              ! 1 = no heat flux
                             ! 2 = prescribe bottom temperature

**********************************************************************************

*** SOLUTE SECTION ***

**********************************************************************************
* Part 0: Specify whether simulation includes solute transport

* Switch for simulation of solute transport
  SWSOLU = 0                 ! 0 = no simulation of solute transport
                             ! 1 = simulation of solute transport

**********************************************************************************

* End of the main input file .SWP!
