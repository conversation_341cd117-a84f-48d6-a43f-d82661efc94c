@echo off
REM ========================================================================
REM SWAP 4.2.0 Windows Build Script using gfortran
REM Alternative to Intel Fortran Compiler
REM ========================================================================

echo Building SWAP 4.2.0 for Windows using gfortran...
echo.

REM Check if gfortran is available
gfortran --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: gfortran not found in PATH
    echo Please install MinGW-w64 or MSYS2 with gfortran
    echo Download from: https://www.msys2.org/
    pause
    exit /b 1
)

REM Create build directories
if not exist "build" mkdir build
if not exist "bin" mkdir bin

REM Change to source directory
cd swap\source_swp_4.2.0

echo Compiling SWAP source files with gfortran...

REM Set compiler flags for gfortran
set FFLAGS=-O2 -fdefault-real-8 -fdefault-double-8 -Wall -Wextra -fcheck=bounds -fbacktrace -J../../build

REM Compile all Fortran source files
gfortran %FFLAGS% -c variables.f90 -o ../../build/variables.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c swap_csv_output.f90 -o ../../build/swap_csv_output.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c sptabulated.f90 -o ../../build/sptabulated.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c WC_K_models_04_11.f90 -o ../../build/WC_K_models_04_11.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c tillage.f90 -o ../../build/tillage.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c tridag.f90 -o ../../build/tridag.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c checkmassbal.f90 -o ../../build/checkmassbal.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c fluxes.f90 -o ../../build/fluxes.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c calcgwl.f90 -o ../../build/calcgwl.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c hysteresis.f90 -o ../../build/hysteresis.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c penmon.f90 -o ../../build/penmon.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c divdra.f90 -o ../../build/divdra.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c sharedsimulation.f90 -o ../../build/sharedsimulation.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c watstor.f90 -o ../../build/watstor.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c sharedexchange.f90 -o ../../build/sharedexchange.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c convertdiscrvert.f90 -o ../../build/convertdiscrvert.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c meteoday.f90 -o ../../build/meteoday.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c oxygenstress.f90 -o ../../build/oxygenstress.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c irrigation.f90 -o ../../build/irrigation.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c rootextraction.f90 -o ../../build/rootextraction.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c frozencond.f90 -o ../../build/frozencond.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c solute.f90 -o ../../build/solute.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c integral.f90 -o ../../build/integral.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c readswap.f90 -o ../../build/readswap.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c timecontrol.f90 -o ../../build/timecontrol.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c temperature.f90 -o ../../build/temperature.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c management_soil.f90 -o ../../build/management_soil.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c soilwater.f90 -o ../../build/soilwater.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c initialize.f90 -o ../../build/initialize.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c readmeteo.f90 -o ../../build/readmeteo.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c drainage.f90 -o ../../build/drainage.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c swapoutput.f90 -o ../../build/swapoutput.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c macrorate.f90 -o ../../build/macrorate.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c calcgrid.f90 -o ../../build/calcgrid.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c snow.f90 -o ../../build/snow.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c boundtop.f90 -o ../../build/boundtop.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c swap.f90 -o ../../build/swap.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c macroporeoutput.f90 -o ../../build/macroporeoutput.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c headcalc.f90 -o ../../build/headcalc.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c boundbottom.f90 -o ../../build/boundbottom.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c cropgrowth.f90 -o ../../build/cropgrowth.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c surfacewater.f90 -o ../../build/surfacewater.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c macropore.f90 -o ../../build/macropore.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c functions.f90 -o ../../build/functions.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c meteodt.f90 -o ../../build/meteodt.o
if errorlevel 1 goto :error

gfortran %FFLAGS% -c swap_main.f90 -o ../../build/swap_main.o
if errorlevel 1 goto :error

echo.
echo Linking executable...

REM Link all object files to create executable
gfortran -O2 -o ../../bin/swap.exe ../../build/*.o
if errorlevel 1 goto :error

cd ..\..

echo.
echo ========================================================================
echo Build completed successfully!
echo Executable created: bin\swap.exe
echo ========================================================================
echo.
goto :end

:error
echo.
echo ========================================================================
echo ERROR: Build failed!
echo Please check the error messages above.
echo ========================================================================
echo.
cd ..\..
pause
exit /b 1

:end
echo You can now run the 1-hupselbrook case with:
echo   cd swap\cases\1.hupselbrook
echo   ..\..\..\bin\swap.exe swap
echo.
pause
