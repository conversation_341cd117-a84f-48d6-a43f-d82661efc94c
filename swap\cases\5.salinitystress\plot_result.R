# Wageningen Environmental Research
# <EMAIL>
#------------------------------------------------

# set start of program
TIMPRGSTART <- Sys.time()

# set modus
test <- FALSE

# read arguments
#------------------------------------------------

if (!test) {
   command_args <- commandArgs(trailingOnly = TRUE)
   if (length(command_args) != 0) {
     warning("ERROR: wrong usage of script")
     warning("Arguments: none")
     stop()
    }
  } else {
   command_args <- NULL
   setwd("d:/cases/5.salinitystress")
  }
ctrl <- command_args[1]

# load libraries
#------------------------------------------------
source("../../Rsoftware/libraries.R")

# run R-script
#------------------------------------------------
message(str_c("\nProgram R-PROG started...\n"))

# ---- initial part of procedure ----

if(!interactive()) pdf(NULL)

file_swp <- "./swap.swp"
file_cwso <- "./observed/cwso.csv"
file_conc <- "./observed/conc.csv"

# ---- main part of procedure ----

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output.csv")
db_swp <- read_csv_SWAP(file = file_csv, variable = c("CPWSO", "CWSO", "TREDDRY", "TREDWET", "TREDSOL")) %>%
  mutate(
    date = as_date(datetime),
    potential = ifelse(CPWSO == 0, NA_real_, CPWSO),
    actual = ifelse(CPWSO == 0, NA_real_, CWSO),
    drought = TREDDRY,
    oxygen = TREDWET,
    salinity = TREDSOL
  ) %>%
  select(date, potential, actual, drought, oxygen, salinity)

# ---- crop development ----

# load observed yield
db_obs <- read_csv(file = file_cwso, col_types = "Dd", lazy = FALSE, comment = "#")

# rearrange data
db_fig <- NULL
levels <- c("potential", "actual")
for (level in levels) {
  db_tmp <- db_swp %>%
    rename(value = all_of(level)) %>%
    mutate(level = all_of(level)) %>%
    select(date, level, value)
  db_fig <- rbind(db_fig, db_tmp)
}

# create factors
db_fig$level <- factor(x = db_fig$level, levels = levels, ordered = TRUE)

# create plot
G1 <- ggplot(data = db_fig) +
  geom_line(aes(x = date, y = value, colour = level), na.rm = TRUE) +
  geom_point(data = db_obs, aes(x = date, y = value), colour = "red") +
  scale_colour_manual(name = create_label(label = "growth"), values = c("potential" = "green", "actual" = "black")) +
  labs(x = "", y = create_label(label = "Yield", unit = "kgds ha-1")) +
  get_my_theme("figure")

# ---- stress ----

# rearrange data
db_fig <- NULL
levels <- c("drought", "oxygen", "salinity")
for (level in levels) {
  db_tmp <- db_swp %>%
    rename(value = all_of(level)) %>%
    mutate(level = all_of(level)) %>%
    select(date, level, value)
  db_fig <- rbind(db_fig, db_tmp)
}

# create factors
db_fig$level <- factor(x = db_fig$level, levels = levels, ordered = TRUE)

# create plot
G2 <- ggplot(data = db_fig) +
  geom_col(aes(x = date, y = value * 10, fill = level), position = "stack") +
  scale_fill_manual(name = create_label(label = "stress"), values = c("drought" = "red", "oxygen" = "blue", "salinity" = "orange")) +
  labs(x = "", y = create_label(label = "Transpiration reduction", unit = "mm d-1")) +
  get_my_theme("figure")

# ---- combine plots ----

# combine
P1 <- ggplotGrob(G1)
P2 <- ggplotGrob(G2)
P <- rbind(P1, P2, size = "first")
P$widths <- unit.pmax(P1$widths, P2$widths)

ggsave(filename = str_c(OUTFIL, ".png"), plot = P, width = 10, height = 8, dpi = "retina", bg = "white")


# ---- solute concentration (timeseries) ----

variable <- "CONC"
depth <- c(-5.0, -25.0, -55.0)

# load observed yield
db_obs <- read_csv(file = file_conc, col_types = "Ddd", lazy = FALSE, comment = "#") %>%
  rename(observed = value)

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output.csv")
extract_variable <- str_c(variable, "[", formatC(x = depth, format = "f", digits = 1), "]")
db_swp <- read_csv_SWAP(file = file_csv, variable = extract_variable) %>%
  mutate(
    date = as_date(datetime)
  ) %>%
  select(date, all_of(extract_variable))

# rearrange data
db_fig <- NULL
for (s_depth in depth) {
  extract_variable <- str_c(variable, "[", formatC(x = s_depth, format = "f", digits = 1), "]")
  db_tmp <- db_swp %>%
    rename(modelled = all_of(extract_variable)) %>%
    mutate(depth = all_of(s_depth)) %>%
    select(date, depth, modelled)
  db_fig <- rbind(db_fig, db_tmp)
}

# combine data
db_fig <- left_join(x = db_fig, y = db_obs, by = c("date", "depth"))

# set limits
ylim <- c(0, ceiling(max(c(db_fig$modelled, db_fig$observed), na.rm = TRUE)))
xpos <- min(db_fig$date)
ypos <- ylim[1] + 0.95 * (ylim[2] - ylim[1])

# set performance
db_prf <- get_modelperformance(actual = db_fig$observed, predicted = db_fig$modelled, performance = c("rmse", "rpearson"), index = db_fig$depth) %>%
  rename(depth = index) %>%
  mutate(
    xpos = xpos,
    ypos = ypos,
    label = str_c("RMSE: ", formatC(x = rmse, format = "f", digits = 2), "  Rpearson: ", formatC(x = rpearson, format = "f", digits = 2))
  )

labels <- NULL
for (s_depth in depth) labels <- c(labels, create_label(label = s_depth, unit = "cm+MSL"))

# create factors
db_fig$depth <- factor(x = db_fig$depth, levels = depth, labels = labels, ordered = TRUE)
db_prf$depth <- factor(x = db_prf$depth, levels = depth, labels = labels, ordered = TRUE)

# create plot
G <- ggplot(data = db_fig) +
  geom_line(aes(x = date, y = modelled), colour = "orange") +
  geom_point(aes(x = date, y = observed), colour = "red", na.rm = TRUE) +
  geom_text(data = db_prf, aes(x = xpos, y = ypos, label = label), colour = "black", hjust = "inward", fontface = "italic") +
  labs(x = "", y = create_label(label = "Salinity concentration ", unit = "mg cm-3")) +
  get_my_theme("figure") +
  facet_wrap(~depth, ncol = 1, labeller = label_parsed)

ggsave(filename = str_c(OUTFIL, "_solute.png"), plot = G, width = 10, height = 8, dpi = "retina", bg = "white")


# ---- solute concentration (contour) ----

# load swap results
OUTFIL <- get_value_SWAP(variable = "OUTFIL", file = file_swp)
file_csv <- str_c(OUTFIL, "_output_tz.csv")
db_swp <- read_csv_SWAP(file = file_csv, variable = c("CONC")) %>%
  mutate(
    date = as_date(datetime),
    depth = as.integer(depth * 10),
    value = CONC
  ) %>%
  select(date, depth, value)

# extract discretization
HSUBLAY <- get_value_SWAP(variable = "SOILPROFILE::HSUBLAY", file = file_swp)
NCOMP <- get_value_SWAP(variable = "SOILPROFILE::NCOMP", file = file_swp)
db_cmp <- NULL
for (rec in 1:length(HSUBLAY)) {
  db_tmp <- tibble(layer = rep(x = rec, times = NCOMP[rec]), hcomp = rep(x = HSUBLAY[rec] / NCOMP[rec], times = NCOMP[rec]))
  db_cmp <- rbind(db_cmp, db_tmp)
}

db_cmp <- db_cmp %>%
  mutate(
    bottom = cumsum(hcomp),
    top = lag(bottom, default = 0),
    depth = as.integer((0.5 * top + 0.5 * bottom) * -10)
  ) %>%
  filter(bottom <= 150) %>%
  select(depth, top, bottom)

# combine data
db_fig <- left_join(x = db_cmp, y = db_swp, by = c("depth"))

# create plot
G <- ggplot(data = db_fig) +
  geom_rect(aes(xmin = date, xmax = date + 1, ymin = -bottom, ymax = -top, fill = value)) +
  scale_fill_gradientn(name = create_label(label = "Salinity conc.", unit = "mg cm-3"), colours = viridis(100)) +
  labs(x = "", y = create_label(label = "Depth ", unit = "cm+MSL")) +
  get_my_theme("figure")

ggsave(filename = str_c(OUTFIL, "_contour.png"), plot = G, width = 10, height = 8, dpi = "retina", bg = "white")


# ---- return part of procedure ----

TIMPRGEND <- Sys.time()
TIMPRGCALC <- as.numeric(difftime(time1 = TIMPRGEND, time2 = TIMPRGSTART, units = "secs"))
message(paste0("\nProgram R-PROG successfully ended in ", floor(TIMPRGCALC / 60), " minutes and ", ceiling(TIMPRGCALC %% 60), " seconds"))
q(save = "no")
