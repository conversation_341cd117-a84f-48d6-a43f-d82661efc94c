@echo off
REM ========================================================================
REM Run SWAP 1-hupselbrook Case Study
REM ========================================================================

echo Running SWAP 1-hupselbrook case study...
echo.

REM Check if executable exists
if not exist "bin\swap.exe" (
    echo ERROR: swap.exe not found in bin directory
    echo Please run build_swap_gfortran.bat first to compile the model
    pause
    exit /b 1
)

REM Change to case directory
cd swap\cases\1.hupselbrook

echo Current directory: %CD%
echo.
echo Input files in this directory:
dir /b *.swp *.met *.crp *.dra 2>nul

echo.
echo Running SWAP model...
echo Command: ..\..\..\bin\swap.exe swap
echo.

REM Run the model
..\..\..\bin\swap.exe swap

if errorlevel 1 (
    echo.
    echo ERROR: SWAP execution failed!
    echo Check the error messages above.
) else (
    echo.
    echo ========================================================================
    echo SWAP execution completed successfully!
    echo ========================================================================
    echo.
    echo Output files generated:
    dir /b result.* *.out *.csv 2>nul
    echo.
    echo You can find the results in the current directory:
    echo %CD%
)

echo.
cd ..\..\..
pause
